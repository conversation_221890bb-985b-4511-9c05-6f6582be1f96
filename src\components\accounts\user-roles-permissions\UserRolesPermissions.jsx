// src/components/accounts/user-roles-permissions/UserRolesPermissions.jsx

import React, { useState } from 'react';
import { useRoles } from '../../../hooks/useRoles';
import { usePermissions } from '../../../hooks/usePermissions';
import RolesList from './components/RolesList';
import RoleDetails from './components/RoleDetails';
import PermissionsGrid from './components/PermissionsGrid';
import UsersTable from './components/UsersTable';
import NewRoleModal from './components/NewRoleModal';
import { toast } from 'react-toastify';
import './styles/roles-permissions.css';

const UserRolesPermissions = () => {
  const {
    roles,
    selectedRole,
    setSelectedRole,
    loading: rolesLoading,
    error: rolesError,
    createRole,
    updateRole,
    deleteRole
  } = useRoles();

  const {
    permissions,
    togglePermission,
    savePermissions,
    loading: permissionsLoading
  } = usePermissions(selectedRole);

  const [isEditing, setIsEditing] = useState(false);
  const [showNewRoleModal, setShowNewRoleModal] = useState(false);

  const handleRoleSelect = (role) => {
    setSelectedRole(role);
    setIsEditing(false);
  };

  const handleSaveRole = async (roleData) => {
    try {
      if (selectedRole) {
        await updateRole(selectedRole.id, roleData);
        toast.success('Role updated successfully');
      }
      setIsEditing(false);
    } catch (error) {
      toast.error('Failed to update role');
    }
  };

  const handleSavePermissions = async () => {
    try {
      await savePermissions();
      toast.success('Permissions updated successfully');
    } catch (error) {
      toast.error('Failed to update permissions');
    }
  };

  const handleCreateRole = async (roleData) => {
    try {
      const newRole = await createRole(roleData);
      setSelectedRole(newRole);
      setShowNewRoleModal(false);
      toast.success('Role created successfully');
    } catch (error) {
      toast.error('Failed to create role');
    }
  };

  const handleDeleteRole = async () => {
    if (!selectedRole || selectedRole.userCount > 0) {
      toast.error('Cannot delete role with assigned users');
      return;
    }

    if (window.confirm(`Are you sure you want to delete the "${selectedRole.name}" role?`)) {
      try {
        await deleteRole(selectedRole.id);
        toast.success('Role deleted successfully');
      } catch (error) {
        toast.error('Failed to delete role');
      }
    }
  };

  if (rolesLoading) {
    return (
      <div className="roles-container">
        <div className="loading-spinner">Loading...</div>
      </div>
    );
  }

  if (rolesError) {
    return (
      <div className="roles-container">
        <div className="error-message">Error: {rolesError}</div>
      </div>
    );
  }

  return (
    <div className="roles-container">
      {/* Header */}
      <div className="roles-header">
        <div className="breadcrumb">
          <span style={{color: '#64748b'}}>Accounts</span> &gt; 
          <span style={{color: '#64748b'}}> Account Management</span> &gt; 
          User Roles &amp; Permissions
        </div>
        <h1 className="page-title">User Roles &amp; Permissions</h1>
        <p className="page-description">
          Manage user roles and configure detailed permissions for your organization
        </p>
      </div>

      {/* Main Content */}
      <div className="main-content">
        {/* Roles Panel */}
        <RolesList
          roles={roles}
          selectedRole={selectedRole}
          onRoleSelect={handleRoleSelect}
          onCreateRole={() => setShowNewRoleModal(true)}
        />

        {/* Role Details & Permissions Panel */}
        <div className="permissions-panel">
          {selectedRole && (
            <>
              <RoleDetails
                role={selectedRole}
                isEditing={isEditing}
                onEdit={() => setIsEditing(true)}
                onSave={handleSaveRole}
                onCancel={() => setIsEditing(false)}
                onDelete={handleDeleteRole}
              />

              <PermissionsGrid
                permissions={permissions}
                onTogglePermission={togglePermission}
                onSave={handleSavePermissions}
                loading={permissionsLoading}
              />
            </>
          )}
        </div>
      </div>

      {/* Users with this Role */}
      {selectedRole && (
        <UsersTable role={selectedRole} />
      )}

      {/* New Role Modal */}
      {showNewRoleModal && (
        <NewRoleModal
          onSave={handleCreateRole}
          onCancel={() => setShowNewRoleModal(false)}
        />
      )}
    </div>
  );
};

export default UserRolesPermissions;