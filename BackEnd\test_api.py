#!/usr/bin/env python3
"""
Simple API Test Script for NeuroIQ User Management System
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000"

def test_health():
    """Test health endpoint"""
    print("Testing health endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ Health check passed")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API server. Make sure it's running on port 8000.")
        return False

def test_register():
    """Test user registration"""
    print("\nTesting user registration...")
    
    user_data = {
        "email": "<EMAIL>",
        "username": "testuser",
        "password": "password123",
        "confirm_password": "password123",
        "first_name": "Test",
        "last_name": "User"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/auth/register", json=user_data)
        if response.status_code == 201:
            print("✅ User registration successful")
            return True
        elif response.status_code == 400:
            print("⚠️  User might already exist")
            return True  # This is okay for testing
        else:
            print(f"❌ Registration failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return False

def test_login():
    """Test user login"""
    print("\nTesting user login...")
    
    # Try with default admin user
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code == 200:
            print("✅ Login successful")
            data = response.json()
            access_token = data.get("tokens", {}).get("access_token")
            if access_token:
                print("✅ Access token received")
                return access_token
            else:
                print("❌ No access token in response")
                return None
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_protected_endpoint(access_token):
    """Test protected endpoint"""
    print("\nTesting protected endpoint...")
    
    headers = {
        "Authorization": f"Bearer {access_token}"
    }
    
    try:
        response = requests.get(f"{BASE_URL}/api/auth/me", headers=headers)
        if response.status_code == 200:
            print("✅ Protected endpoint access successful")
            data = response.json()
            print(f"Current user: {data.get('username')} ({data.get('email')})")
            return True
        else:
            print(f"❌ Protected endpoint failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Protected endpoint error: {e}")
        return False

def test_api_docs():
    """Test API documentation endpoints"""
    print("\nTesting API documentation...")
    
    try:
        # Test OpenAPI JSON
        response = requests.get(f"{BASE_URL}/openapi.json")
        if response.status_code == 200:
            print("✅ OpenAPI JSON accessible")
        else:
            print(f"❌ OpenAPI JSON failed: {response.status_code}")
            return False
        
        # Test Swagger UI
        response = requests.get(f"{BASE_URL}/docs")
        if response.status_code == 200:
            print("✅ Swagger UI accessible")
        else:
            print(f"❌ Swagger UI failed: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ API docs error: {e}")
        return False

def main():
    """Run all tests"""
    print("NeuroIQ API Test Suite")
    print("=" * 50)
    
    # Test health
    if not test_health():
        print("\n❌ Basic connectivity failed. Exiting.")
        sys.exit(1)
    
    # Test registration
    test_register()
    
    # Test login
    access_token = test_login()
    if not access_token:
        print("\n❌ Login failed. Cannot test protected endpoints.")
        sys.exit(1)
    
    # Test protected endpoint
    test_protected_endpoint(access_token)
    
    # Test API docs
    test_api_docs()
    
    print("\n" + "=" * 50)
    print("✅ API test suite completed!")
    print("\nAPI Endpoints:")
    print(f"- Health: {BASE_URL}/health")
    print(f"- API Docs: {BASE_URL}/docs")
    print(f"- ReDoc: {BASE_URL}/redoc")
    print(f"- OpenAPI: {BASE_URL}/openapi.json")

if __name__ == "__main__":
    main()
