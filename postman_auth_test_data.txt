AUTHENTICATION ENDPOINTS TEST DATA FOR POSTMAN
==============================================

Base URL: http://localhost:8000 (adjust according to your server)

==============================================
1. POST /api/auth/register - Register a new user
==============================================

Method: POST
URL: {{base_url}}/api/auth/register
Headers: 
  Content-Type: application/json

Body (JSON):
{
  "email": "<EMAIL>",
  "username": "johndoe",
  "password": "SecurePass123!",
  "confirm_password": "SecurePass123!",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "phone": "+1234567890"
}

Alternative test data:
{
  "email": "<EMAIL>",
  "username": "jane<PERSON>",
  "password": "MyPassword456!",
  "confirm_password": "MyPassword456!",
  "first_name": "Jane",
  "last_name": "Smith"
}

==============================================
2. POST /api/auth/login - Login user
==============================================

Method: POST
URL: {{base_url}}/api/auth/login
Headers: 
  Content-Type: application/json

Body (JSON):
{
  "username": "johndoe",
  "password": "SecurePass123!"
}

Alternative (using email):
{
  "username": "<EMAIL>",
  "password": "SecurePass123!"
}

Expected Response: Save the access_token and refresh_token for subsequent requests

==============================================
3. POST /api/auth/refresh - Refresh access token
==============================================

Method: POST
URL: {{base_url}}/api/auth/refresh
Headers: 
  Content-Type: application/json

Body (JSON):
{
  "refresh_token": "{{refresh_token_from_login}}"
}

Note: Replace {{refresh_token_from_login}} with the actual refresh token received from login

==============================================
4. POST /api/auth/logout - Logout user
==============================================

Method: POST
URL: {{base_url}}/api/auth/logout
Headers: 
  Content-Type: application/json
  Authorization: Bearer {{access_token}}

Body: No body required

Note: Replace {{access_token}} with the actual access token received from login

==============================================
5. GET /api/auth/me - Get current user info
==============================================

Method: GET
URL: {{base_url}}/api/auth/me
Headers: 
  Authorization: Bearer {{access_token}}

Body: No body required

Note: Replace {{access_token}} with the actual access token received from login

==============================================
6. POST /api/auth/password-reset/request - Request password reset
==============================================

Method: POST
URL: {{base_url}}/api/auth/password-reset/request
Headers: 
  Content-Type: application/json

Body (JSON):
{
  "email": "<EMAIL>"
}

Alternative:
{
  "email": "<EMAIL>"
}

==============================================
7. POST /api/auth/password-reset/confirm - Confirm password reset
==============================================

Method: POST
URL: {{base_url}}/api/auth/password-reset/confirm
Headers: 
  Content-Type: application/json

Body (JSON):
{
  "token": "reset_token_received_via_email",
  "new_password": "NewSecurePass789!",
  "confirm_password": "NewSecurePass789!"
}

Note: Replace "reset_token_received_via_email" with actual token from email

==============================================
8. POST /api/auth/email-verification/request - Request email verification
==============================================

Method: POST
URL: {{base_url}}/api/auth/email-verification/request
Headers: 
  Content-Type: application/json

Body (JSON):
{
  "email": "<EMAIL>"
}

Alternative:
{
  "email": "<EMAIL>"
}

==============================================
9. POST /api/auth/email-verification/confirm - Confirm email verification
==============================================

Method: POST
URL: {{base_url}}/api/auth/email-verification/confirm
Headers: 
  Content-Type: application/json

Body (JSON):
{
  "token": "verification_token_received_via_email"
}

Note: Replace "verification_token_received_via_email" with actual token from email

==============================================
POSTMAN ENVIRONMENT VARIABLES SETUP
==============================================

Create a Postman Environment with these variables:
- base_url: http://localhost:8000
- access_token: (will be set automatically from login response)
- refresh_token: (will be set automatically from login response)

==============================================
TESTING WORKFLOW RECOMMENDATIONS
==============================================

1. First, test user registration with the register endpoint
2. Then test login to get tokens
3. Use the access token for protected endpoints (logout, me)
4. Test token refresh functionality
5. Test password reset flow (request -> confirm)
6. Test email verification flow (request -> confirm)

==============================================
ADDITIONAL TEST SCENARIOS
==============================================

Invalid Registration Data:
{
  "email": "invalid-email",
  "username": "ab",
  "password": "123",
  "confirm_password": "456"
}

Invalid Login Data:
{
  "username": "nonexistent",
  "password": "wrongpassword"
}

==============================================
EXPECTED HTTP STATUS CODES
==============================================

- Register: 201 Created (success), 400 Bad Request (validation error)
- Login: 200 OK (success), 401 Unauthorized (invalid credentials)
- Refresh: 200 OK (success), 401 Unauthorized (invalid token)
- Logout: 200 OK (success), 401 Unauthorized (invalid token)
- Me: 200 OK (success), 401 Unauthorized (no/invalid token)
- Password Reset Request: 200 OK (success), 400 Bad Request (invalid email)
- Password Reset Confirm: 200 OK (success), 400 Bad Request (invalid token/data)
- Email Verification Request: 200 OK (success), 400 Bad Request (invalid email)
- Email Verification Confirm: 200 OK (success), 400 Bad Request (invalid token)
