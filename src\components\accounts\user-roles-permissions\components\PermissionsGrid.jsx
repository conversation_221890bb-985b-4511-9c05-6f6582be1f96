import React from 'react';
import { PERMISSION_CATEGORIES } from '../../../../data/permissionsData';

const PermissionsGrid = ({ permissions, onTogglePermission, onSave, loading }) => {
  return (
    <div className="permissions-section">
      <div className="panel-header" style={{ marginBottom: '20px' }}>
        <h3 className="panel-title">Permissions</h3>
        <button
          className="btn-primary"
          onClick={onSave}
          disabled={loading}
        >
          {loading ? 'Saving...' : 'Save Permissions'}
        </button>
      </div>

      <div className="permissions-grid">
        {Object.values(PERMISSION_CATEGORIES).map((category) => (
          <div key={category.id} className="permission-category">
            <div className="category-header">
              <h4 className="category-title">{category.title}</h4>
              <p className="category-description">{category.description}</p>
            </div>
            
            <div className="permissions-list">
              {category.permissions.map((permission) => (
                <div key={permission.id} className="permission-item">
                  <div className="permission-info">
                    <h5>{permission.name}</h5>
                    <p>{permission.description}</p>
                  </div>
                  <div
                    className={`permission-toggle ${permissions[permission.id] ? 'active' : ''}`}
                    onClick={() => onTogglePermission(permission.id)}
                  />
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PermissionsGrid;