from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Dict, Any

from ..models.auth import (
    Token, LoginResponse, RefreshTokenRequest, CurrentUser,
    PasswordResetRequest, PasswordResetConfirm,
    EmailVerificationRequest, EmailVerificationConfirm
)
from ..models.user import User<PERSON>ogin, UserCreate, UserResponse
from ..services.auth_service import AuthService
from ..services.user_service import UserService

router = APIRouter(prefix="/auth", tags=["Authentication"])
security = HTTPBearer()

# Initialize services
auth_service = AuthService()
user_service = UserService()

@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register(user_data: UserCreate, request: Request):
    """Register a new user"""
    try:
        # Create user
        user = await user_service.create_user(user_data)
        
        # Log registration activity
        await auth_service.log_activity(
            user.id,
            "registration",
            "User registered successfully",
            request.client.host if request.client else None,
            request.headers.get("user-agent")
        )
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )

@router.post("/login", response_model=LoginResponse)
async def login(login_data: UserLogin, request: Request):
    """Login user and return tokens"""
    try:
        return await auth_service.login(login_data, request)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )

@router.post("/refresh", response_model=Token)
async def refresh_token(refresh_data: RefreshTokenRequest):
    """Refresh access token"""
    try:
        return await auth_service.refresh_token(refresh_data.refresh_token)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )

@router.post("/logout")
async def logout(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Logout user and invalidate session"""
    try:
        # Get current user to get user_id
        current_user = await auth_service.get_current_user(credentials.credentials)
        
        # Logout
        success = await auth_service.logout(credentials.credentials, current_user.id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Logout failed"
            )
        
        return {"message": "Successfully logged out"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )

@router.get("/me", response_model=CurrentUser)
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get current user information"""
    try:
        return await auth_service.get_current_user(credentials.credentials)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials"
        )

@router.post("/password-reset/request")
async def request_password_reset(reset_data: PasswordResetRequest):
    """Request password reset"""
    try:
        # Check if user exists
        user = await user_service.get_user_by_email(reset_data.email)
        
        if not user:
            # Don't reveal if email exists or not for security
            return {"message": "If the email exists, a password reset link has been sent"}
        
        # TODO: Implement password reset token generation and email sending
        # For now, just return success message
        
        return {"message": "If the email exists, a password reset link has been sent"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset request failed"
        )

@router.post("/password-reset/confirm")
async def confirm_password_reset(reset_data: PasswordResetConfirm):
    """Confirm password reset with token"""
    try:
        # Validate passwords match
        reset_data.validate_passwords_match()
        
        # TODO: Implement password reset token validation and password update
        # For now, just return success message
        
        return {"message": "Password has been reset successfully"}
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset failed"
        )

@router.post("/email-verification/request")
async def request_email_verification(verification_data: EmailVerificationRequest):
    """Request email verification"""
    try:
        # Check if user exists
        user = await user_service.get_user_by_email(verification_data.email)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        if user.is_verified:
            return {"message": "Email is already verified"}
        
        # TODO: Implement email verification token generation and email sending
        # For now, just return success message
        
        return {"message": "Verification email has been sent"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Email verification request failed"
        )

@router.post("/email-verification/confirm")
async def confirm_email_verification(verification_data: EmailVerificationConfirm):
    """Confirm email verification with token"""
    try:
        # TODO: Implement email verification token validation and user verification
        # For now, just return success message
        
        return {"message": "Email has been verified successfully"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Email verification failed"
        )

# Dependency to get current user
async def get_current_user_dependency(credentials: HTTPAuthorizationCredentials = Depends(security)) -> CurrentUser:
    """Dependency to get current authenticated user"""
    return await auth_service.get_current_user(credentials.credentials)
