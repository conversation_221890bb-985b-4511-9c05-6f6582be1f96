#!/bin/bash

echo "NeuroIQ User Management API Setup"
echo "================================"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed"
    echo "Please install Python 3.8+ and try again"
    exit 1
fi

echo "Python found!"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install requirements
echo "Installing requirements..."
pip install -r requirements.txt

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "Error: .env file not found!"
    echo "Please create a .env file with your database configuration"
    exit 1
fi

# Setup database
echo "Setting up database..."
python setup_database.py

if [ $? -ne 0 ]; then
    echo "Database setup failed!"
    exit 1
fi

echo ""
echo "================================"
echo "Setup completed successfully!"
echo ""
echo "Starting the API server..."
echo "API will be available at: http://localhost:8000"
echo "API Documentation: http://localhost:8000/docs"
echo ""
echo "Press Ctrl+C to stop the server"
echo "================================"
echo ""

# Start the server
python run.py
