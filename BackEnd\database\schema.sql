-- NeuroIQ User Management Database Schema
-- Create database if not exists
CREATE DATABASE IF NOT EXISTS NeuroIQ;
USE NeuroIQ;

-- 1. Core Users Table
CREATE TABLE IF NOT EXISTS neuroiq_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(100),
    last_name VA<PERSON><PERSON><PERSON>(100),
    phone VARCHAR(20),
    avatar_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_active (is_active)
);

-- 2. User Roles Table
CREATE TABLE IF NOT EXISTS user_roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    role_name VARCHAR(50) UNIQUE NOT NULL,
    role_description TEXT,
    permissions JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 3. User Role Assignments Table
CREATE TABLE IF NOT EXISTS user_role_assignments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    role_id INT NOT NULL,
    assigned_by INT,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES neuroiq_users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES neuroiq_users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_user_role (user_id, role_id)
);

-- 4. Identity Providers Table
CREATE TABLE IF NOT EXISTS identity_providers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    provider_name VARCHAR(50) NOT NULL,
    provider_type ENUM('oauth2', 'saml', 'oidc', 'local') DEFAULT 'local',
    client_id VARCHAR(255),
    client_secret VARCHAR(255),
    discovery_url VARCHAR(500),
    redirect_uri VARCHAR(500),
    scopes JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 5. User External Identities Table
CREATE TABLE IF NOT EXISTS user_external_identities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    provider_id INT NOT NULL,
    external_user_id VARCHAR(255) NOT NULL,
    external_username VARCHAR(255),
    external_email VARCHAR(255),
    access_token TEXT,
    refresh_token TEXT,
    token_expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES neuroiq_users(id) ON DELETE CASCADE,
    FOREIGN KEY (provider_id) REFERENCES identity_providers(id) ON DELETE CASCADE,
    UNIQUE KEY unique_provider_external_id (provider_id, external_user_id)
);

-- 6. User Sessions Table
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    refresh_token VARCHAR(255) UNIQUE,
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES neuroiq_users(id) ON DELETE CASCADE,
    INDEX idx_session_token (session_token),
    INDEX idx_refresh_token (refresh_token),
    INDEX idx_user_active (user_id, is_active)
);

-- 7. User Activity Log Table
CREATE TABLE IF NOT EXISTS user_activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    activity_type VARCHAR(50) NOT NULL,
    activity_description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES neuroiq_users(id) ON DELETE SET NULL,
    INDEX idx_user_activity (user_id, activity_type),
    INDEX idx_activity_date (created_at)
);

-- 8. Password Reset Tokens Table
CREATE TABLE IF NOT EXISTS password_reset_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES neuroiq_users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_user_token (user_id, is_used)
);

-- 9. Email Verification Tokens Table
CREATE TABLE IF NOT EXISTS email_verification_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES neuroiq_users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_user_token (user_id, is_used)
);


CREATE TABLE identity_providers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    provider_name VARCHAR(100) NOT NULL UNIQUE COMMENT 'Provider name (azure_ad, google, facebook, etc.)',
    provider_type ENUM('oauth2', 'oidc', 'saml') NOT NULL COMMENT 'Authentication protocol type',
    client_id VARCHAR(255) NOT NULL COMMENT 'OAuth client ID or SAML entity ID',
    client_secret VARCHAR(500) COMMENT 'OAuth client secret (encrypted)',
    issuer_url VARCHAR(500) COMMENT 'OIDC issuer URL or SAML metadata URL',
    authorization_url VARCHAR(500) COMMENT 'OAuth authorization URL',
    token_url VARCHAR(500) COMMENT 'OAuth token URL',
    userinfo_url VARCHAR(500) COMMENT 'User info endpoint URL',
    scopes TEXT COMMENT 'Required OAuth scopes (JSON array)',
    config_json JSON COMMENT 'Additional provider-specific configuration',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Whether the provider is active',
   
    -- Audit Fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by BIGINT,
   
    -- Foreign Keys
    FOREIGN KEY (created_by) REFERENCES neuroiq_users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES neuroiq_users(id) ON DELETE SET NULL,
   
    -- Indexes
    INDEX idx_provider_name (provider_name),
    INDEX idx_provider_type (provider_type),
    INDEX idx_is_active (is_active)
);

-- External Groups table (replaces Azure AD groups)
CREATE TABLE external_groups (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    provider_id BIGINT NOT NULL COMMENT 'Reference to identity provider',
    external_group_id VARCHAR(255) NOT NULL COMMENT 'External group ID from provider',
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    email VARCHAR(255),
    membership_type ENUM('dynamic', 'assigned') DEFAULT 'assigned',
    group_type ENUM('security', 'distribution', 'unified', 'google_workspace', 'custom') DEFAULT 'security',
    mail_enabled BOOLEAN DEFAULT FALSE,
    security_enabled BOOLEAN DEFAULT TRUE,
   
    -- Audit Fields
    synced_from_idp_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by BIGINT,
   
    -- Foreign Keys
    FOREIGN KEY (provider_id) REFERENCES identity_providers(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES neuroiq_users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES neuroiq_users(id) ON DELETE SET NULL,
   
    -- Indexes
    UNIQUE KEY uk_provider_group (provider_id, external_group_id),
    INDEX idx_external_group_id (external_group_id),
    INDEX idx_display_name (display_name),
    INDEX idx_group_type (group_type),
    INDEX idx_security_enabled (security_enabled)
);

-- User Group Memberships Table
CREATE TABLE user_group_memberships (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    group_id BIGINT NOT NULL,
    membership_type ENUM('member', 'owner', 'guest') DEFAULT 'member',
    synced_from_idp_at TIMESTAMP NULL,
   
    -- Audit Fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by BIGINT,
   
    -- Foreign Keys
    FOREIGN KEY (user_id) REFERENCES neuroiq_users(id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES external_groups(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES neuroiq_users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES neuroiq_users(id) ON DELETE SET NULL,
   
    -- Indexes
    INDEX idx_user_id (user_id),
    INDEX idx_group_id (group_id),
    INDEX idx_membership_type (membership_type),
    UNIQUE KEY unique_user_group (user_id, group_id)
);