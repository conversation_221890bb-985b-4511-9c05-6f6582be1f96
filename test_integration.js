#!/usr/bin/env node

/**
 * Integration Test Script for R-NeuroIQ Admin Portal
 * Tests the connection between React frontend and FastAPI backend
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:8000';

// Test data
const testUser = {
  email: '<EMAIL>',
  username: 'testuser',
  password: 'testpassword123',
  confirm_password: 'testpassword123',
  first_name: 'Test',
  last_name: 'User',
  phone: '+**********'
};

const testLogin = {
  username: 'testuser',
  password: 'testpassword123'
};

async function testHealthCheck() {
  console.log('🔍 Testing health check...');
  try {
    const response = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health check passed:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Health check failed:', error.message);
    return false;
  }
}

async function testUserRegistration() {
  console.log('\n🔍 Testing user registration...');
  try {
    const response = await axios.post(`${BASE_URL}/api/auth/register`, testUser);
    console.log('✅ User registration successful:', response.data);
    return response.data;
  } catch (error) {
    if (error.response?.status === 400) {
      console.log('⚠️ User already exists (expected for repeated tests)');
      return null;
    }
    console.log('❌ User registration failed:', error.response?.data || error.message);
    return null;
  }
}

async function testUserLogin() {
  console.log('\n🔍 Testing user login...');
  try {
    const response = await axios.post(`${BASE_URL}/api/auth/login`, testLogin);
    console.log('✅ User login successful');
    console.log('   User:', response.data.user.username);
    console.log('   Roles:', response.data.user.roles);
    console.log('   Token received:', !!response.data.tokens.access_token);
    return response.data.tokens.access_token;
  } catch (error) {
    console.log('❌ User login failed:', error.response?.data || error.message);
    return null;
  }
}

async function testProtectedEndpoint(token) {
  console.log('\n🔍 Testing protected endpoint...');
  try {
    const response = await axios.get(`${BASE_URL}/api/users/me`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('✅ Protected endpoint access successful:', response.data);
    return response.data;
  } catch (error) {
    console.log('❌ Protected endpoint failed:', error.response?.data || error.message);
    return null;
  }
}

async function testRolesEndpoint(token) {
  console.log('\n🔍 Testing roles endpoint...');
  try {
    const response = await axios.get(`${BASE_URL}/api/rbac/roles`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('✅ Roles endpoint successful');
    console.log('   Roles found:', response.data.length);
    response.data.forEach(role => {
      console.log(`   - ${role.role_name}: ${role.role_description}`);
    });
    return response.data;
  } catch (error) {
    console.log('❌ Roles endpoint failed:', error.response?.data || error.message);
    return null;
  }
}

async function testCORS() {
  console.log('\n🔍 Testing CORS configuration...');
  try {
    const response = await axios.options(`${BASE_URL}/api/auth/login`);
    console.log('✅ CORS preflight successful');
    return true;
  } catch (error) {
    console.log('❌ CORS test failed:', error.message);
    return false;
  }
}

async function runIntegrationTests() {
  console.log('🚀 Starting R-NeuroIQ Integration Tests');
  console.log('=' * 50);

  // Test 1: Health Check
  const healthOk = await testHealthCheck();
  if (!healthOk) {
    console.log('\n❌ Backend is not running. Please start the FastAPI server first.');
    console.log('   Run: cd BackEnd && python run.py');
    process.exit(1);
  }

  // Test 2: CORS
  await testCORS();

  // Test 3: User Registration
  await testUserRegistration();

  // Test 4: User Login
  const token = await testUserLogin();
  if (!token) {
    console.log('\n❌ Cannot proceed with protected endpoint tests without valid token');
    process.exit(1);
  }

  // Test 5: Protected Endpoint
  await testProtectedEndpoint(token);

  // Test 6: RBAC Endpoint
  await testRolesEndpoint(token);

  console.log('\n' + '=' * 50);
  console.log('✅ Integration tests completed!');
  console.log('\n📋 Next Steps:');
  console.log('1. Start the React frontend: npm start');
  console.log('2. Navigate to http://localhost:3000');
  console.log('3. Test user registration and login in the UI');
  console.log('4. Test role management functionality');
}

// Run tests
runIntegrationTests().catch(error => {
  console.error('❌ Test suite failed:', error.message);
  process.exit(1);
});
