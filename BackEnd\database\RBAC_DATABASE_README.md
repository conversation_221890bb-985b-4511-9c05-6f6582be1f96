# NeuroIQ RBAC Database Setup

This directory contains all the necessary SQL scripts and setup tools for implementing a comprehensive Role-Based Access Control (RBAC) system in the NeuroIQ Admin Portal.

## 📁 Files Overview

### Core SQL Scripts
- **`schema.sql`** - Main database schema (existing)
- **`rbac_schema.sql`** - Enhanced RBAC tables, indexes, and views
- **`rbac_init_data.sql`** - Default roles, permissions, and initial data
- **`setup_rbac_database.sql`** - Safe database enhancement script
- **`init_data.sql`** - Original initial data (existing)

### Setup Scripts
- **`run_rbac_setup.sh`** - Linux/macOS setup script
- **`run_rbac_setup.bat`** - Windows setup script

### Documentation
- **`RBAC_DATABASE_README.md`** - This file

## 🚀 Quick Setup

### Prerequisites
- MySQL 5.7+ or MariaDB 10.3+
- MySQL client tools installed
- Database user with CREATE, ALTER, INSERT, UPDATE, DELETE privileges

### Option 1: Automated Setup (Recommended)

#### For Linux/macOS:
```bash
cd BackEnd/database
chmod +x run_rbac_setup.sh
./run_rbac_setup.sh
```

#### For Windows:
```cmd
cd BackEnd\database
run_rbac_setup.bat
```

### Option 2: Manual Setup

1. **Create Database** (if not exists):
```sql
CREATE DATABASE IF NOT EXISTS NeuroIQ;
USE NeuroIQ;
```

2. **Run Main Schema** (if not already done):
```bash
mysql -u root -p NeuroIQ < schema.sql
```

3. **Apply RBAC Enhancements**:
```bash
mysql -u root -p NeuroIQ < setup_rbac_database.sql
```

4. **Add Additional RBAC Tables**:
```bash
mysql -u root -p NeuroIQ < rbac_schema.sql
```

5. **Insert Initial Data**:
```bash
mysql -u root -p NeuroIQ < rbac_init_data.sql
```

## 📊 Database Schema Overview

### Enhanced Core Tables

#### `user_roles` (Enhanced)
- **New Columns**: `created_by`, `updated_by`, `role_level`, `is_system_role`, `max_assignments`
- **Purpose**: Hierarchical role management with system protection
- **Indexes**: Optimized for role queries and hierarchy

#### `user_role_assignments` (Enhanced)
- **New Columns**: `assignment_reason`, `revoked_at`, `revoked_by`, `revocation_reason`
- **Purpose**: Complete audit trail for role assignments
- **Indexes**: Optimized for user-role queries

### New RBAC Tables

#### `permission_categories`
- **Purpose**: Organize permissions into logical categories
- **Features**: Display ordering, activation status

#### `permissions`
- **Purpose**: Granular permission definitions
- **Features**: Resource-based permissions, action types, system protection

#### `role_permissions`
- **Purpose**: Many-to-many mapping between roles and permissions
- **Features**: Granular permission assignment, audit trail

#### `role_hierarchy`
- **Purpose**: Role inheritance system
- **Features**: Parent-child relationships, inheritance types

#### `user_permission_overrides`
- **Purpose**: User-specific permission grants/denials
- **Features**: Override role permissions, expiration support

#### `rbac_audit_log`
- **Purpose**: Complete audit trail for all RBAC operations
- **Features**: Action tracking, before/after values, IP logging

### Useful Views

#### `v_user_roles`
- **Purpose**: Complete user-role information with status
- **Usage**: Easy querying of user role assignments

#### `v_role_assignment_summary`
- **Purpose**: Role usage statistics and capacity management
- **Usage**: Administrative overview of role assignments

#### `v_user_effective_permissions`
- **Purpose**: Calculated effective permissions per user
- **Usage**: Permission checking and debugging

## 🔐 Default Roles and Permissions

### System Roles (Cannot be deleted)

#### `system_admin` (Level 10)
- **Purpose**: Super administrator with all permissions
- **Permissions**: `all_permissions: true`
- **Use Case**: System maintenance and configuration

#### `admin` (Level 8)
- **Purpose**: Administrative user management
- **Key Permissions**: User management, role assignment, audit access
- **Use Case**: Day-to-day administration

#### `manager` (Level 5)
- **Purpose**: Team management and oversight
- **Key Permissions**: User viewing, team management, limited role assignment
- **Use Case**: Department heads, team leaders

#### `user` (Level 2)
- **Purpose**: Standard application user
- **Key Permissions**: Profile management, basic data access
- **Use Case**: Regular application users

#### `guest` (Level 1)
- **Purpose**: Limited access for temporary users
- **Key Permissions**: Profile viewing only
- **Use Case**: Trial users, temporary access

### Specialized Roles

#### `data_analyst` (Level 4)
- **Purpose**: Analytics and reporting specialist
- **Key Permissions**: Data access, analytics, report creation

#### `content_manager` (Level 4)
- **Purpose**: Content and data management
- **Key Permissions**: Data read/write, import/export

#### `security_officer` (Level 6)
- **Purpose**: Security and audit oversight
- **Key Permissions**: Audit access, security management

#### `api_user` (Level 3)
- **Purpose**: API-only access for integrations
- **Key Permissions**: API read/write, data access

#### `read_only` (Level 2)
- **Purpose**: Read-only access across the system
- **Key Permissions**: Read access to most features

#### `team_lead` (Level 5)
- **Purpose**: Team leadership with limited admin rights
- **Key Permissions**: Team management, role assignment

## 🔧 Configuration

### Database Connection
Update the connection parameters in the setup scripts:

```bash
# In run_rbac_setup.sh or run_rbac_setup.bat
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="NeuroIQ"
DB_USER="root"
DB_PASSWORD=""  # Will prompt if empty
```

### Customization
After setup, you can:

1. **Add Custom Roles**:
```sql
INSERT INTO user_roles (role_name, role_description, permissions, role_level) 
VALUES ('custom_role', 'Description', '{"permission": true}', 3);
```

2. **Create Custom Permissions**:
```sql
INSERT INTO permissions (permission_name, permission_description, resource_type, action_type) 
VALUES ('custom_permission', 'Description', 'resource', 'read');
```

3. **Assign Roles to Users**:
```sql
INSERT INTO user_role_assignments (user_id, role_id, assigned_by) 
VALUES (1, 2, 1);
```

## 🔍 Verification

After setup, verify the installation:

```sql
-- Check tables exist
SHOW TABLES LIKE '%role%';
SHOW TABLES LIKE '%permission%';

-- Check default roles
SELECT role_name, role_level, is_system_role FROM user_roles ORDER BY role_level DESC;

-- Check permissions
SELECT COUNT(*) as permission_count FROM permissions;

-- Check views
SELECT COUNT(*) as view_count FROM information_schema.views 
WHERE table_schema = 'NeuroIQ' AND table_name LIKE 'v_%';
```

## 🚨 Troubleshooting

### Common Issues

1. **Permission Denied**:
   - Ensure MySQL user has sufficient privileges
   - Grant necessary permissions: `GRANT ALL ON NeuroIQ.* TO 'user'@'localhost';`

2. **Table Already Exists**:
   - Scripts are designed to be idempotent
   - Safe to re-run if needed

3. **Foreign Key Constraints**:
   - Ensure main schema is applied first
   - Check that referenced tables exist

4. **Character Set Issues**:
   - Ensure database uses UTF-8: `ALTER DATABASE NeuroIQ CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;`

### Rollback
If you need to rollback the RBAC setup:

```sql
-- Remove RBAC-specific tables (be careful!)
DROP VIEW IF EXISTS v_user_effective_permissions;
DROP VIEW IF EXISTS v_role_assignment_summary;
DROP VIEW IF EXISTS v_user_roles;
DROP TABLE IF EXISTS rbac_audit_log;
DROP TABLE IF EXISTS user_permission_overrides;
DROP TABLE IF EXISTS role_hierarchy;
DROP TABLE IF EXISTS role_permissions;
DROP TABLE IF EXISTS permissions;
DROP TABLE IF EXISTS permission_categories;

-- Remove added columns (optional)
ALTER TABLE user_roles 
DROP COLUMN IF EXISTS created_by,
DROP COLUMN IF EXISTS updated_by,
DROP COLUMN IF EXISTS role_level,
DROP COLUMN IF EXISTS is_system_role,
DROP COLUMN IF EXISTS max_assignments;
```

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the API documentation
3. Check the application logs
4. Verify database connectivity and permissions

## 🔄 Updates

When updating the RBAC system:
1. Always backup your database first
2. Test changes in a development environment
3. Run setup scripts to apply new enhancements
4. Verify functionality after updates
