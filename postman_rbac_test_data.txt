RBAC (ROLE-BASED ACCESS CONTROL) ENDPOINTS TEST DATA FOR POSTMAN
================================================================

Base URL: http://localhost:8000 (adjust according to your server)

IMPORTANT: You need to be logged in as an admin user with role_management permissions to test most of these endpoints.

================================================================
ROLE MANAGEMENT ENDPOINTS
================================================================

1. POST /api/rbac/roles - Create a new role
================================================================

Method: POST
URL: {{base_url}}/api/rbac/roles
Headers: 
  Content-Type: application/json
  Authorization: Bearer {{access_token}}

Body (JSON):
{
  "role_name": "data_analyst",
  "role_description": "Data analyst with read access to analytics",
  "permissions": {
    "user_view": true,
    "analytics_read": true,
    "reports_read": true,
    "dashboard_access": true
  }
}

Alternative test data:
{
  "role_name": "team_lead",
  "role_description": "Team leader with team management permissions",
  "permissions": {
    "user_view": true,
    "team_management": true,
    "user_settings": false,
    "role_assignment": true
  }
}

================================================================
2. GET /api/rbac/roles - Get all roles
================================================================

Method: GET
URL: {{base_url}}/api/rbac/roles
Headers: 
  Authorization: Bearer {{access_token}}

Query Parameters (optional):
- include_inactive: false

Alternative with inactive roles:
URL: {{base_url}}/api/rbac/roles?include_inactive=true

================================================================
3. GET /api/rbac/roles/{role_id} - Get role by ID
================================================================

Method: GET
URL: {{base_url}}/api/rbac/roles/1
Headers: 
  Authorization: Bearer {{access_token}}

Note: Replace "1" with actual role ID

================================================================
4. PUT /api/rbac/roles/{role_id} - Update role
================================================================

Method: PUT
URL: {{base_url}}/api/rbac/roles/1
Headers: 
  Content-Type: application/json
  Authorization: Bearer {{access_token}}

Body (JSON):
{
  "role_description": "Updated data analyst role with enhanced permissions",
  "permissions": {
    "user_view": true,
    "analytics_read": true,
    "analytics_write": true,
    "reports_read": true,
    "reports_write": true,
    "dashboard_access": true
  }
}

================================================================
5. DELETE /api/rbac/roles/{role_id} - Delete role (soft delete)
================================================================

Method: DELETE
URL: {{base_url}}/api/rbac/roles/1
Headers: 
  Authorization: Bearer {{access_token}}

Body: No body required

================================================================
ROLE ASSIGNMENT ENDPOINTS
================================================================

6. POST /api/rbac/users/{user_id}/roles/{role_id} - Assign role to user
================================================================

Method: POST
URL: {{base_url}}/api/rbac/users/2/roles/3
Headers: 
  Content-Type: application/json
  Authorization: Bearer {{access_token}}

Body (JSON - optional):
{
  "expires_at": "2024-12-31T23:59:59"
}

Note: Replace "2" with actual user ID and "3" with actual role ID

================================================================
7. DELETE /api/rbac/users/{user_id}/roles/{role_id} - Remove role from user
================================================================

Method: DELETE
URL: {{base_url}}/api/rbac/users/2/roles/3
Headers: 
  Authorization: Bearer {{access_token}}

Body: No body required

================================================================
8. GET /api/rbac/users/{user_id}/roles - Get user's roles
================================================================

Method: GET
URL: {{base_url}}/api/rbac/users/2/roles
Headers: 
  Authorization: Bearer {{access_token}}

Note: Users can view their own roles, or admin can view any user's roles

================================================================
9. GET /api/rbac/roles/{role_id}/users - Get users with specific role
================================================================

Method: GET
URL: {{base_url}}/api/rbac/roles/3/users
Headers: 
  Authorization: Bearer {{access_token}}

Note: Requires user_management permission

================================================================
PERMISSION CHECKING ENDPOINTS
================================================================

10. GET /api/rbac/users/{user_id}/permissions - Get user's permissions
================================================================

Method: GET
URL: {{base_url}}/api/rbac/users/2/permissions
Headers: 
  Authorization: Bearer {{access_token}}

Note: Users can view their own permissions, or admin can view any user's permissions

================================================================
11. GET /api/rbac/users/{user_id}/permissions/{permission} - Check specific permission
================================================================

Method: GET
URL: {{base_url}}/api/rbac/users/2/permissions/user_management
Headers: 
  Authorization: Bearer {{access_token}}

Alternative examples:
- {{base_url}}/api/rbac/users/2/permissions/role_management
- {{base_url}}/api/rbac/users/2/permissions/analytics_read

================================================================
BULK OPERATIONS
================================================================

12. POST /api/rbac/users/{user_id}/roles/bulk - Assign multiple roles
================================================================

Method: POST
URL: {{base_url}}/api/rbac/users/2/roles/bulk
Headers: 
  Content-Type: application/json
  Authorization: Bearer {{access_token}}

Body (JSON):
{
  "role_ids": [3, 4, 5]
}

================================================================
13. DELETE /api/rbac/users/{user_id}/roles/bulk - Remove multiple roles
================================================================

Method: DELETE
URL: {{base_url}}/api/rbac/users/2/roles/bulk
Headers: 
  Content-Type: application/json
  Authorization: Bearer {{access_token}}

Body (JSON):
{
  "role_ids": [3, 4]
}

================================================================
TESTING WORKFLOW RECOMMENDATIONS
================================================================

1. **Setup**: First ensure you have an admin user with role_management permissions
2. **Create Roles**: Test creating different types of roles with various permissions
3. **View Roles**: Test getting all roles and specific roles by ID
4. **Update Roles**: Test updating role descriptions and permissions
5. **Assign Roles**: Test assigning roles to users
6. **Check Permissions**: Test permission checking endpoints
7. **Bulk Operations**: Test bulk role assignments and removals
8. **Cleanup**: Test role removal and deletion

================================================================
SAMPLE ROLE DEFINITIONS FOR TESTING
================================================================

Basic User Role:
{
  "role_name": "basic_user",
  "role_description": "Basic user with minimal permissions",
  "permissions": {
    "user_view": true,
    "profile_edit": true
  }
}

Manager Role:
{
  "role_name": "manager",
  "role_description": "Manager with team oversight permissions",
  "permissions": {
    "user_view": true,
    "user_management": false,
    "team_management": true,
    "role_assignment": true,
    "reports_read": true
  }
}

Admin Role:
{
  "role_name": "admin",
  "role_description": "Administrator with full user management",
  "permissions": {
    "user_management": true,
    "role_assignment": true,
    "user_settings": true,
    "audit_access": true
  }
}

================================================================
EXPECTED HTTP STATUS CODES
================================================================

- Create Role: 201 Created (success), 400 Bad Request (role exists), 403 Forbidden (no permission)
- Get Roles: 200 OK (success), 403 Forbidden (no permission)
- Get Role by ID: 200 OK (success), 404 Not Found, 403 Forbidden (no permission)
- Update Role: 200 OK (success), 404 Not Found, 403 Forbidden (no permission)
- Delete Role: 200 OK (success), 404 Not Found, 400 Bad Request (role in use), 403 Forbidden
- Assign Role: 201 Created (success), 400 Bad Request (already assigned), 404 Not Found
- Remove Role: 200 OK (success), 404 Not Found, 403 Forbidden (no permission)
- Get User Roles: 200 OK (success), 403 Forbidden (no permission)
- Get Role Users: 200 OK (success), 403 Forbidden (no permission)
- Check Permissions: 200 OK (success), 403 Forbidden (no permission)

================================================================
PERMISSION REQUIREMENTS
================================================================

- role_management: Required for creating, updating, deleting roles
- role_assignment: Required for assigning/removing roles (or role_management)
- user_management: Required for viewing other users' roles and permissions
- all_permissions: Super admin access (bypasses all permission checks)

================================================================
POSTMAN ENVIRONMENT VARIABLES
================================================================

Make sure you have these variables set in your Postman environment:
- base_url: http://localhost:8000
- access_token: (obtained from login)
- user_id: (ID of a test user for role assignments)
- role_id: (ID of a test role)

================================================================
ERROR TESTING SCENARIOS
================================================================

Test with insufficient permissions:
- Try creating roles without role_management permission
- Try assigning roles without role_assignment permission
- Try viewing other users' data without user_management permission

Test with invalid data:
- Try creating role with existing name
- Try assigning non-existent role
- Try assigning role to non-existent user
- Try deleting role that's assigned to users
