import React, { useState } from 'react';
import { BrowserRouter as Router } from 'react-router-dom';
import {
  LayoutDashboard,
  Users,
  MessageSquare,
  Bot,
  Shield,
  Settings,
  Plug,
  ChevronDown,
  ChevronRight,
  Bell,
  Search,
  User,
  Menu,
  X,
  LogOut
} from 'lucide-react';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import CreateNewUser from './components/CreateNewUser';
import UserRolesPermissions from './components/accounts/user-roles-permissions';
import Login from './components/Login';
import { AuthProvider, useAuth } from './contexts/AuthContext';

const EnterpriseAIAdmin = () => {
  const { isAuthenticated, isLoading, user, logout } = useAuth();
  const [activeMenuItem, setActiveMenuItem] = useState('dashboard');
  const [expandedMenus, setExpandedMenus] = useState({});
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [currentView, setCurrentView] = useState('dashboard'); // New state for view management

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Show login if not authenticated
  if (!isAuthenticated) {
    return <Login onLoginSuccess={() => {}} />;
  }

  const menuItems = [
    {
      id: 'dashboard',
      title: 'Dashboard',
      icon: LayoutDashboard,
      component: DashboardContent
    },
    {
      id: 'accounts',
      title: 'Accounts',
      icon: Users,
      subItems: [
        {
          id: 'account-management',
          title: 'Account Management',
          subOptions: [
            { id: 'all-users', title: 'All Users' },
            { id: 'create-new-user', title: 'Create New User' },
            { id: 'user-roles', title: 'User Roles & Permissions' },
            { id: 'group-memberships', title: 'Group Memberships' },
            { id: 'identity-provider', title: 'Identity Provider Sync' }
          ]
        },
        {
          id: 'personalization-settings',
          title: 'Personalization Settings',
          subOptions: [
            { id: 'view-edit-settings', title: 'View/Edit Settings' },
            { id: 'role-based-settings', title: 'Role-Based Settings' },
            { id: 'settings-inheritance', title: 'Settings Inheritance' },
            { id: 'settings-versioning', title: 'Settings Versioning' }
          ]
        },
        {
          id: 'preferences',
          title: 'Preferences',
          subOptions: [
            { id: 'general-preferences', title: 'General Preferences' },
            { id: 'ai-interaction', title: 'AI Interaction Preferences' },
            { id: 'workflow-preferences', title: 'Workflow Preferences' }
          ]
        },
        {
          id: 'memory-management',
          title: 'Memory Management',
          subOptions: [
            { id: 'all-memories', title: 'All Memories' },
            { id: 'create-memory', title: 'Create New Memory' },
            { id: 'memory-categories', title: 'Memory Categories' },
            { id: 'semantic-search', title: 'Semantic Search' },
            { id: 'memory-logs', title: 'Memory Access Logs' }
          ]
        }
      ]
    },
    {
      id: 'chat-history',
      title: 'Chat History',
      icon: MessageSquare,
      subOptions: [
        { id: 'chat-sessions', title: 'Chat Sessions' },
        { id: 'messages', title: 'Messages' },
        { id: 'memory-references', title: 'Memory References' },
        { id: 'engagement-metrics', title: 'Engagement Metrics' }
      ]
    },
    {
      id: 'assistants-agents',
      title: 'Assistants & Agents',
      icon: Bot,
      subOptions: [
        { id: 'assistant-access', title: 'Assistant Access Control' },
        { id: 'agent-access', title: 'Agent Access Control' },
        { id: 'usage-quotas', title: 'Usage Quotas' },
        { id: 'access-logs', title: 'Access Logs' }
      ]
    },
    {
      id: 'security',
      title: 'Security',
      icon: Shield,
      subOptions: [
        { id: 'jwt-management', title: 'JWT Token Management' },
        { id: 'identity-providers', title: 'Identity Providers' },
        { id: 'rbac', title: 'Role-Based Access Control' },
        { id: 'audit-logs', title: 'Audit Logs' },
        { id: 'encryption', title: 'Data Encryption Settings' }
      ]
    },
    {
      id: 'integration-center',
      title: 'Integration Center',
      icon: Plug,
      subOptions: [
        { id: 'frontend-integration', title: 'Frontend Integration' },
        { id: 'ai-integration', title: 'AI System Integration' },
        { id: 'external-sync', title: 'External System Sync' },
        { id: 'monitoring', title: 'Monitoring & Alerts' }
      ]
    },
    {
      id: 'configuration',
      title: 'Configuration',
      icon: Settings,
      subOptions: [
        { id: 'env-variables', title: 'Environment Variables' },
        { id: 'auth-settings', title: 'Authentication Settings' },
        { id: 'db-config', title: 'Database Configuration' },
        { id: 'app-settings', title: 'Application Settings' }
      ]
    }
  ];

  const toggleMenu = (menuId) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuId]: !prev[menuId]
    }));
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleMenuClick = (itemId, subItemId = null) => {
    if (itemId === 'create-new-user' || subItemId === 'create-new-user') {
      setCurrentView('create-new-user');
    } else if (itemId === 'user-roles' || subItemId === 'user-roles') {
      setCurrentView('user-roles');
    } else if (itemId === 'dashboard') {
      setCurrentView('dashboard');
    } else {
      setCurrentView('default');
    }
    setActiveMenuItem(itemId);
  };

  const handleBackToDashboard = () => {
    setCurrentView('dashboard');
    setActiveMenuItem('dashboard');
  };

  const handleLogout = async () => {
    try {
      await logout();
      // User will be redirected to login automatically
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const renderContent = () => {
    switch (currentView) {
      case 'create-new-user':
        return <CreateNewUser onBack={handleBackToDashboard} />;
      case 'user-roles':
        return <UserRolesPermissions />;
      case 'dashboard':
        return <DashboardContent />;
      default:
        return <DefaultContent title={activeMenuItem} />;
    }
  };

  const getPageTitle = () => {
    switch (currentView) {
      case 'create-new-user':
        return 'Create New User';
      case 'user-roles':
        return 'User Roles & Permissions';
      case 'dashboard':
        return 'Dashboard';
      default:
        return activeMenuItem.replace('-', ' ');
    }
  };

  return (
    <AuthProvider>
      <Router>
        <div className="flex h-screen bg-gray-50">
        {/* Sidebar */}
        <div className={`${sidebarOpen ? 'w-64' : 'w-16'} transition-all duration-300 bg-white shadow-lg border-r border-gray-200 flex flex-col`}>
          {/* Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              {sidebarOpen && (
                <h1 className="text-xl font-bold text-gray-800">
                  <span className="text-blue-600">Enterprise</span>AI
                </h1>
              )}
              <button
                onClick={toggleSidebar}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                {sidebarOpen ? <X size={20} /> : <Menu size={20} />}
              </button>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 overflow-y-auto py-4">
            {menuItems.map((item) => (
              <div key={item.id} className="mb-1">
                <button
                  onClick={() => {
                    if (item.subItems || item.subOptions) {
                      toggleMenu(item.id);
                    }
                    handleMenuClick(item.id);
                  }}
                  className={`w-full flex items-center px-4 py-3 text-left hover:bg-gray-50 transition-colors ${
                    activeMenuItem === item.id ? 'bg-blue-50 border-r-2 border-blue-500' : ''
                  }`}
                >
                  <item.icon size={20} className={`${activeMenuItem === item.id ? 'text-blue-600' : 'text-gray-500'}`} />
                  {sidebarOpen && (
                    <>
                      <span className={`ml-3 font-medium ${activeMenuItem === item.id ? 'text-blue-600' : 'text-gray-700'}`}>
                        {item.title}
                      </span>
                      {(item.subItems || item.subOptions) && (
                        <div className="ml-auto">
                          {expandedMenus[item.id] ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                        </div>
                      )}
                    </>
                  )}
                </button>

                {/* Sub Items */}
                {sidebarOpen && expandedMenus[item.id] && (item.subItems || item.subOptions) && (
                  <div className="ml-8 mt-1">
                    {item.subItems ? (
                      item.subItems.map((subItem) => (
                        <div key={subItem.id} className="mb-2">
                          <button
                            onClick={() => toggleMenu(subItem.id)}
                            className="w-full flex items-center px-3 py-2 text-left hover:bg-gray-50 transition-colors rounded-lg"
                          >
                            <span className="text-sm font-medium text-gray-600">{subItem.title}</span>
                            <div className="ml-auto">
                              {expandedMenus[subItem.id] ? <ChevronDown size={14} /> : <ChevronRight size={14} />}
                            </div>
                          </button>
                          {expandedMenus[subItem.id] && (
                            <div className="ml-4 mt-1">
                              {subItem.subOptions.map((option, index) => (
                                <button
                                  key={option.id || index}
                                  onClick={() => handleMenuClick(option.id || option.title, option.id)}
                                  className={`w-full text-left px-3 py-1.5 text-xs transition-colors rounded ${
                                    currentView === option.id ? 'text-blue-600 bg-blue-50' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                                  }`}
                                >
                                  {typeof option === 'string' ? option : option.title}
                                </button>
                              ))}
                            </div>
                          )}
                        </div>
                      ))
                    ) : (
                      item.subOptions.map((option, index) => (
                        <button
                          key={option.id || index}
                          onClick={() => handleMenuClick(option.id || option.title)}
                          className="w-full text-left px-3 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg"
                        >
                          {typeof option === 'string' ? option : option.title}
                        </button>
                      ))
                    )}
                  </div>
                )}
              </div>
            ))}
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Top Bar */}
          <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-semibold text-gray-800 capitalize">
                {getPageTitle()}
              </h2>
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search..."
                    className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <button className="p-2 rounded-lg hover:bg-gray-100 relative">
                  <Bell size={20} className="text-gray-600" />
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                </button>
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <User size={16} className="text-white" />
                  </div>
                  <span className="font-medium text-gray-700">
                    {user?.first_name || user?.username || 'Admin'}
                  </span>
                  <button
                    onClick={handleLogout}
                    className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                    title="Logout"
                  >
                    <LogOut size={16} className="text-gray-600" />
                  </button>
                </div>
              </div>
            </div>
          </header>

          {/* Content Area */}
          <main className="flex-1 overflow-y-auto p-6">
            {renderContent()}
          </main>
        </div>

        {/* Toast Container for notifications */}
        <ToastContainer
          position="top-right"
          autoClose={3000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
        />
        </div>
      </Router>
    </AuthProvider>
  );
};

// Dashboard Content Component
function DashboardContent() {
  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users size={24} className="text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">2,847</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <MessageSquare size={24} className="text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Chat Sessions</p>
              <p className="text-2xl font-bold text-gray-900">15,239</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Bot size={24} className="text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Agents</p>
              <p className="text-2xl font-bold text-gray-900">127</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Shield size={24} className="text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Security Events</p>
              <p className="text-2xl font-bold text-gray-900">3</p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts and Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">System Usage Analytics</h3>
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <p className="text-gray-500">Usage Analytics Chart</p>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Recent Activity</h3>
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map((item) => (
              <div key={item} className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div>
                  <p className="text-sm font-medium text-gray-800">User action performed</p>
                  <p className="text-xs text-gray-500">2 minutes ago</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Notifications */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Notifications</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div>
              <p className="font-medium text-blue-800">System Update Available</p>
              <p className="text-sm text-blue-600">New features and security improvements</p>
            </div>
            <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              Update
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Default Content Component
function DefaultContent({ title }) {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
      <div className="text-center">
        <h3 className="text-2xl font-semibold text-gray-800 mb-4 capitalize">
          {title.replace('-', ' ')} Module
        </h3>
        <p className="text-gray-600 mb-6">
          This section is under development. The {title.replace('-', ' ')} functionality will be available soon.
        </p>
        <div className="bg-gray-50 rounded-lg p-6">
          <p className="text-sm text-gray-500">
            Content for {title} will be implemented here with all the specified sub-options and features.
          </p>
        </div>
      </div>
    </div>
  );
}

export default EnterpriseAIAdmin;