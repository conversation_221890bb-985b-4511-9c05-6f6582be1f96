#!/bin/bash

# NeuroIQ RBAC Database Setup Script
# This script sets up the complete RBAC system by running all SQL scripts in the correct order

# Configuration
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="NeuroIQ"
DB_USER="root"
DB_PASSWORD=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to execute SQL file
execute_sql_file() {
    local file=$1
    local description=$2
    
    print_status "Executing $description..."
    
    if [ ! -f "$file" ]; then
        print_error "File $file not found!"
        return 1
    fi
    
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$file"; then
        print_success "$description completed successfully"
        return 0
    else
        print_error "$description failed!"
        return 1
    fi
}

# Function to check if MySQL is accessible
check_mysql_connection() {
    print_status "Checking MySQL connection..."
    
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" > /dev/null 2>&1; then
        print_success "MySQL connection successful"
        return 0
    else
        print_error "Cannot connect to MySQL. Please check your connection parameters."
        return 1
    fi
}

# Function to check if database exists
check_database_exists() {
    print_status "Checking if database $DB_NAME exists..."
    
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" > /dev/null 2>&1; then
        print_success "Database $DB_NAME exists"
        return 0
    else
        print_warning "Database $DB_NAME does not exist. Creating it..."
        if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "CREATE DATABASE IF NOT EXISTS $DB_NAME;"; then
            print_success "Database $DB_NAME created successfully"
            return 0
        else
            print_error "Failed to create database $DB_NAME"
            return 1
        fi
    fi
}

# Main execution function
main() {
    echo "=============================================="
    echo "NeuroIQ RBAC Database Setup"
    echo "=============================================="
    echo ""
    
    # Get database password if not provided
    if [ -z "$DB_PASSWORD" ]; then
        echo -n "Enter MySQL password for user $DB_USER: "
        read -s DB_PASSWORD
        echo ""
    fi
    
    # Check MySQL connection
    if ! check_mysql_connection; then
        exit 1
    fi
    
    # Check if database exists
    if ! check_database_exists; then
        exit 1
    fi
    
    echo ""
    print_status "Starting RBAC setup process..."
    echo ""
    
    # Step 1: Run main schema if it doesn't exist
    if [ -f "schema.sql" ]; then
        print_status "Checking if main schema needs to be applied..."
        # Check if core tables exist
        if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "DESCRIBE neuroiq_users;" > /dev/null 2>&1; then
            execute_sql_file "schema.sql" "Main database schema"
            if [ $? -ne 0 ]; then
                print_error "Main schema setup failed. Aborting."
                exit 1
            fi
        else
            print_success "Main schema already exists"
        fi
    else
        print_warning "Main schema.sql not found. Assuming it's already applied."
    fi
    
    # Step 2: Apply RBAC enhancements
    execute_sql_file "setup_rbac_database.sql" "RBAC database enhancements"
    if [ $? -ne 0 ]; then
        print_error "RBAC setup failed. Aborting."
        exit 1
    fi
    
    # Step 3: Apply additional RBAC schema
    execute_sql_file "rbac_schema.sql" "Additional RBAC tables and views"
    if [ $? -ne 0 ]; then
        print_warning "Additional RBAC schema failed, but continuing..."
    fi
    
    # Step 4: Insert initial RBAC data
    execute_sql_file "rbac_init_data.sql" "RBAC initial data and default roles"
    if [ $? -ne 0 ]; then
        print_warning "RBAC initial data failed, but continuing..."
    fi
    
    # Step 5: Apply existing init_data.sql if it exists
    if [ -f "init_data.sql" ]; then
        execute_sql_file "init_data.sql" "Additional initial data"
        if [ $? -ne 0 ]; then
            print_warning "Additional initial data failed, but continuing..."
        fi
    fi
    
    echo ""
    echo "=============================================="
    print_success "RBAC Setup Complete!"
    echo "=============================================="
    echo ""
    print_status "Summary of what was set up:"
    echo "  ✓ Enhanced user_roles table with hierarchy and system role support"
    echo "  ✓ Enhanced user_role_assignments table with audit trail"
    echo "  ✓ Permission categories and granular permissions system"
    echo "  ✓ Role-permission mapping tables"
    echo "  ✓ RBAC audit logging system"
    echo "  ✓ Useful views for querying role assignments"
    echo "  ✓ Default roles with comprehensive permissions"
    echo "  ✓ Role hierarchy for permission inheritance"
    echo ""
    print_status "Next steps:"
    echo "  1. Test the RBAC API endpoints"
    echo "  2. Create admin users and assign appropriate roles"
    echo "  3. Customize roles and permissions for your specific needs"
    echo ""
}

# Run the main function
main "$@"
