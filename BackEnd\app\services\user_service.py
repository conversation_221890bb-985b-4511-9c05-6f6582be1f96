from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import logging
from fastapi import HTTPException, status

from ..config.database import db_manager
from ..models.user import (
    UserCreate, UserUpdate, UserResponse, UserDetailResponse, 
    UserListResponse, UserSearchParams, UserPasswordUpdate
)
from ..utils.security import security

logger = logging.getLogger(__name__)

class UserService:
    def __init__(self):
        self.db = db_manager
    
    async def create_user(self, user_data: UserCreate) -> UserResponse:
        """Create a new user"""
        try:
            # Check if user already exists
            existing_user = await self.get_user_by_email_or_username(user_data.email, user_data.username)
            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="User with this email or username already exists"
                )
            
            # Hash password
            hashed_password = security.get_password_hash(user_data.password)
            
            # Insert user
            query = """
                INSERT INTO neuroiq_users (
                    email, username, password_hash, first_name, last_name, phone, avatar_url
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            params = (
                user_data.email,
                user_data.username,
                hashed_password,
                user_data.first_name,
                user_data.last_name,
                user_data.phone,
                user_data.avatar_url
            )
            
            user_id = await self.db.execute_insert(query, params)
            
            # Assign default user role
            await self.assign_default_role(user_id)
            
            # Get and return the created user
            return await self.get_user_by_id(user_id)
            
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create user"
            )
    
    async def get_user_by_id(self, user_id: int) -> Optional[UserResponse]:
        """Get user by ID"""
        try:
            query = """
                SELECT u.*, GROUP_CONCAT(r.role_name) as roles
                FROM neuroiq_users u
                LEFT JOIN user_role_assignments ura ON u.id = ura.user_id AND ura.is_active = TRUE
                LEFT JOIN user_roles r ON ura.role_id = r.id AND r.is_active = TRUE
                WHERE u.id = %s AND u.is_active = TRUE
                GROUP BY u.id
            """
            result = await self.db.execute_query(query, (user_id,))
            
            if not result:
                return None
            
            user_data = result[0]
            user_data['roles'] = user_data['roles'].split(',') if user_data['roles'] else []
            
            return UserResponse(**user_data)
            
        except Exception as e:
            logger.error(f"Error getting user by ID: {e}")
            return None
    
    async def get_user_by_email(self, email: str) -> Optional[UserResponse]:
        """Get user by email"""
        try:
            query = """
                SELECT u.*, GROUP_CONCAT(r.role_name) as roles
                FROM neuroiq_users u
                LEFT JOIN user_role_assignments ura ON u.id = ura.user_id AND ura.is_active = TRUE
                LEFT JOIN user_roles r ON ura.role_id = r.id AND r.is_active = TRUE
                WHERE u.email = %s AND u.is_active = TRUE
                GROUP BY u.id
            """
            result = await self.db.execute_query(query, (email,))
            
            if not result:
                return None
            
            user_data = result[0]
            user_data['roles'] = user_data['roles'].split(',') if user_data['roles'] else []
            
            return UserResponse(**user_data)
            
        except Exception as e:
            logger.error(f"Error getting user by email: {e}")
            return None
    
    async def get_user_by_username(self, username: str) -> Optional[UserResponse]:
        """Get user by username"""
        try:
            query = """
                SELECT u.*, GROUP_CONCAT(r.role_name) as roles
                FROM neuroiq_users u
                LEFT JOIN user_role_assignments ura ON u.id = ura.user_id AND ura.is_active = TRUE
                LEFT JOIN user_roles r ON ura.role_id = r.id AND r.is_active = TRUE
                WHERE u.username = %s AND u.is_active = TRUE
                GROUP BY u.id
            """
            result = await self.db.execute_query(query, (username,))
            
            if not result:
                return None
            
            user_data = result[0]
            user_data['roles'] = user_data['roles'].split(',') if user_data['roles'] else []
            
            return UserResponse(**user_data)
            
        except Exception as e:
            logger.error(f"Error getting user by username: {e}")
            return None
    
    async def get_user_by_email_or_username(self, email: str, username: str) -> Optional[UserResponse]:
        """Get user by email or username"""
        try:
            query = """
                SELECT u.*, GROUP_CONCAT(r.role_name) as roles
                FROM neuroiq_users u
                LEFT JOIN user_role_assignments ura ON u.id = ura.user_id AND ura.is_active = TRUE
                LEFT JOIN user_roles r ON ura.role_id = r.id AND r.is_active = TRUE
                WHERE (u.email = %s OR u.username = %s) AND u.is_active = TRUE
                GROUP BY u.id
            """
            result = await self.db.execute_query(query, (email, username))
            
            if not result:
                return None
            
            user_data = result[0]
            user_data['roles'] = user_data['roles'].split(',') if user_data['roles'] else []
            
            return UserResponse(**user_data)
            
        except Exception as e:
            logger.error(f"Error getting user by email or username: {e}")
            return None

    async def update_user(self, user_id: int, user_data: UserUpdate) -> Optional[UserResponse]:
        """Update user information"""
        try:
            # Build dynamic update query
            update_fields = []
            params = []

            if user_data.first_name is not None:
                update_fields.append("first_name = %s")
                params.append(user_data.first_name)

            if user_data.last_name is not None:
                update_fields.append("last_name = %s")
                params.append(user_data.last_name)

            if user_data.phone is not None:
                update_fields.append("phone = %s")
                params.append(user_data.phone)

            if user_data.avatar_url is not None:
                update_fields.append("avatar_url = %s")
                params.append(user_data.avatar_url)

            if not update_fields:
                return await self.get_user_by_id(user_id)

            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            params.append(user_id)

            query = f"""
                UPDATE neuroiq_users
                SET {', '.join(update_fields)}
                WHERE id = %s AND is_active = TRUE
            """

            affected_rows = await self.db.execute_update(query, tuple(params))

            if affected_rows == 0:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            return await self.get_user_by_id(user_id)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating user: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update user"
            )

    async def update_password(self, user_id: int, password_data: UserPasswordUpdate) -> bool:
        """Update user password"""
        try:
            # Get current user
            user = await self.get_user_by_id(user_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            # Get current password hash
            query = "SELECT password_hash FROM neuroiq_users WHERE id = %s"
            result = await self.db.execute_query(query, (user_id,))

            if not result:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            current_hash = result[0]['password_hash']

            # Verify current password
            if not security.verify_password(password_data.current_password, current_hash):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Current password is incorrect"
                )

            # Hash new password
            new_hash = security.get_password_hash(password_data.new_password)

            # Update password
            update_query = """
                UPDATE neuroiq_users
                SET password_hash = %s, updated_at = CURRENT_TIMESTAMP
                WHERE id = %s
            """

            await self.db.execute_update(update_query, (new_hash, user_id))

            return True

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating password: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update password"
            )

    async def assign_default_role(self, user_id: int) -> bool:
        """Assign default user role to a new user"""
        try:
            # Get default user role
            role_query = "SELECT id FROM user_roles WHERE role_name = 'user' AND is_active = TRUE"
            role_result = await self.db.execute_query(role_query)

            if not role_result:
                logger.warning("Default 'user' role not found")
                return False

            role_id = role_result[0]['id']

            # Assign role
            assignment_query = """
                INSERT INTO user_role_assignments (user_id, role_id, assigned_by)
                VALUES (%s, %s, %s)
            """

            await self.db.execute_insert(assignment_query, (user_id, role_id, user_id))

            return True

        except Exception as e:
            logger.error(f"Error assigning default role: {e}")
            return False
