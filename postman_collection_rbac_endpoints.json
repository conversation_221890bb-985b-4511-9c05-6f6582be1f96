{"info": {"name": "R-NeuroIQ RBAC API", "description": "Role-Based Access Control endpoints for R-NeuroIQ Admin Portal", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "user_id", "value": "2", "type": "string"}, {"key": "role_id", "value": "3", "type": "string"}], "item": [{"name": "Role Management", "item": [{"name": "1. Create Role", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"role_name\": \"data_analyst\",\n  \"role_description\": \"Data analyst with read access to analytics\",\n  \"permissions\": {\n    \"user_view\": true,\n    \"analytics_read\": true,\n    \"reports_read\": true,\n    \"dashboard_access\": true\n  }\n}"}, "url": {"raw": "{{base_url}}/api/rbac/roles", "host": ["{{base_url}}"], "path": ["api", "rbac", "roles"]}}}, {"name": "2. Get All Roles", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/rbac/roles", "host": ["{{base_url}}"], "path": ["api", "rbac", "roles"]}}}, {"name": "3. Get Role by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/rbac/roles/{{role_id}}", "host": ["{{base_url}}"], "path": ["api", "rbac", "roles", "{{role_id}}"]}}}, {"name": "4. Update Role", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"role_description\": \"Updated data analyst role with enhanced permissions\",\n  \"permissions\": {\n    \"user_view\": true,\n    \"analytics_read\": true,\n    \"analytics_write\": true,\n    \"reports_read\": true,\n    \"reports_write\": true,\n    \"dashboard_access\": true\n  }\n}"}, "url": {"raw": "{{base_url}}/api/rbac/roles/{{role_id}}", "host": ["{{base_url}}"], "path": ["api", "rbac", "roles", "{{role_id}}"]}}}, {"name": "5. Delete Role", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/rbac/roles/{{role_id}}", "host": ["{{base_url}}"], "path": ["api", "rbac", "roles", "{{role_id}}"]}}}]}, {"name": "Role Assignments", "item": [{"name": "6. Assign Role to User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"expires_at\": \"2024-12-31T23:59:59\"\n}"}, "url": {"raw": "{{base_url}}/api/rbac/users/{{user_id}}/roles/{{role_id}}", "host": ["{{base_url}}"], "path": ["api", "rbac", "users", "{{user_id}}", "roles", "{{role_id}}"]}}}, {"name": "7. <PERSON><PERSON><PERSON> from User", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/rbac/users/{{user_id}}/roles/{{role_id}}", "host": ["{{base_url}}"], "path": ["api", "rbac", "users", "{{user_id}}", "roles", "{{role_id}}"]}}}, {"name": "8. Get User Roles", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/rbac/users/{{user_id}}/roles", "host": ["{{base_url}}"], "path": ["api", "rbac", "users", "{{user_id}}", "roles"]}}}, {"name": "9. Get Role Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/rbac/roles/{{role_id}}/users", "host": ["{{base_url}}"], "path": ["api", "rbac", "roles", "{{role_id}}", "users"]}}}]}, {"name": "Permission Checking", "item": [{"name": "10. Get User Permissions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/rbac/users/{{user_id}}/permissions", "host": ["{{base_url}}"], "path": ["api", "rbac", "users", "{{user_id}}", "permissions"]}}}, {"name": "11. Check Specific Permission", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/rbac/users/{{user_id}}/permissions/user_management", "host": ["{{base_url}}"], "path": ["api", "rbac", "users", "{{user_id}}", "permissions", "user_management"]}}}]}, {"name": "Bulk Operations", "item": [{"name": "12. <PERSON><PERSON> Multiple Roles", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"role_ids\": [3, 4, 5]\n}"}, "url": {"raw": "{{base_url}}/api/rbac/users/{{user_id}}/roles/bulk", "host": ["{{base_url}}"], "path": ["api", "rbac", "users", "{{user_id}}", "roles", "bulk"]}}}, {"name": "13. <PERSON><PERSON><PERSON> Multiple Roles", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"role_ids\": [3, 4]\n}"}, "url": {"raw": "{{base_url}}/api/rbac/users/{{user_id}}/roles/bulk", "host": ["{{base_url}}"], "path": ["api", "rbac", "users", "{{user_id}}", "roles", "bulk"]}}}]}]}