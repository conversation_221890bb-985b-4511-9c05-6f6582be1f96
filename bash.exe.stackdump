Stack trace:
Frame         Function      Args
0007FFFFBF80  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAE80) msys-2.0.dll+0x1FE8E
0007FFFFBF80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC258) msys-2.0.dll+0x67F9
0007FFFFBF80  000210046832 (000210286019, 0007FFFFBE38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBF80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBF80  000210068E24 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC260  00021006A225 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF2DA60000 ntdll.dll
7FFF2CE70000 KERNEL32.DLL
7FFF2B210000 KERNELBASE.dll
7FFF2CAC0000 USER32.dll
7FFF2AE50000 win32u.dll
7FFF2BA40000 GDI32.dll
7FFF2AC70000 gdi32full.dll
7FFF2B750000 msvcp_win.dll
7FFF2B600000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF2CD30000 advapi32.dll
7FFF2C130000 msvcrt.dll
7FFF2C820000 sechost.dll
7FFF2C010000 RPCRT4.dll
7FFF2A1B0000 CRYPTBASE.DLL
7FFF2ADB0000 bcryptPrimitives.dll
7FFF2CFA0000 IMM32.DLL
