// Authentication Context
// Provides authentication state and methods throughout the app

import React, { createContext, useContext, useState, useEffect } from 'react';
import authService from '../services/authService';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [permissions, setPermissions] = useState({});

  // Initialize authentication state
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      const token = authService.getStoredToken();
      if (token) {
        // Verify token by getting current user
        const currentUser = await authService.getCurrentUser();
        setUser(currentUser);
        setIsAuthenticated(true);
        setPermissions(currentUser.permissions || {});
      }
    } catch (error) {
      console.error('Auth initialization error:', error);
      // Token is invalid, clear it
      await authService.logout();
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (credentials) => {
    try {
      setIsLoading(true);
      const result = await authService.login(credentials);
      
      if (result.success) {
        setUser(result.user);
        setIsAuthenticated(true);
        setPermissions(result.permissions || {});
        return { success: true, user: result.user };
      } else {
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      await authService.logout();
      setUser(null);
      setIsAuthenticated(false);
      setPermissions({});
      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData) => {
    try {
      setIsLoading(true);
      const result = await authService.register(userData);
      return result;
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  };

  const updateUser = (updatedUser) => {
    setUser(updatedUser);
  };

  const hasPermission = (permission) => {
    if (!permissions) return false;
    
    // Check for all_permissions (super admin)
    if (permissions.all_permissions) return true;
    
    // Check specific permission
    return permissions[permission] === true;
  };

  const hasAnyPermission = (permissionList) => {
    if (!Array.isArray(permissionList)) return false;
    return permissionList.some(permission => hasPermission(permission));
  };

  const hasAllPermissions = (permissionList) => {
    if (!Array.isArray(permissionList)) return false;
    return permissionList.every(permission => hasPermission(permission));
  };

  const refreshUserData = async () => {
    try {
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);
      setPermissions(currentUser.permissions || {});
      return { success: true, user: currentUser };
    } catch (error) {
      console.error('Refresh user data error:', error);
      return { success: false, error: error.message };
    }
  };

  const value = {
    // State
    user,
    isAuthenticated,
    isLoading,
    permissions,
    
    // Methods
    login,
    logout,
    register,
    updateUser,
    refreshUserData,
    
    // Permission helpers
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
