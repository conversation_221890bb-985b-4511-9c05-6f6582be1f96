-- NeuroIQ RBAC Database Setup Script
-- This script sets up the complete RBAC system including schema enhancements and initial data
-- Run this script after the main schema.sql has been executed

USE NeuroIQ;

-- ============================================================================
-- STEP 1: BACKUP EXISTING DATA (if any)
-- ============================================================================

-- Create temporary backup tables for existing role assignments
CREATE TABLE IF NOT EXISTS temp_role_assignments_backup AS
SELECT * FROM user_role_assignments WHERE 1=0;

INSERT INTO temp_role_assignments_backup 
SELECT * FROM user_role_assignments;

-- ============================================================================
-- STEP 2: ENHANCE EXISTING RBAC TABLES
-- ============================================================================

-- Add new columns to user_roles table if they don't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'user_roles' 
     AND table_schema = 'NeuroIQ' 
     AND column_name = 'created_by') = 0,
    'ALTER TABLE user_roles ADD COLUMN created_by INT',
    'SELECT "Column created_by already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'user_roles' 
     AND table_schema = 'NeuroIQ' 
     AND column_name = 'updated_by') = 0,
    'ALTER TABLE user_roles ADD COLUMN updated_by INT',
    'SELECT "Column updated_by already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'user_roles' 
     AND table_schema = 'NeuroIQ' 
     AND column_name = 'role_level') = 0,
    'ALTER TABLE user_roles ADD COLUMN role_level INT DEFAULT 1 COMMENT "Role hierarchy level (1=lowest, 10=highest)"',
    'SELECT "Column role_level already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'user_roles' 
     AND table_schema = 'NeuroIQ' 
     AND column_name = 'is_system_role') = 0,
    'ALTER TABLE user_roles ADD COLUMN is_system_role BOOLEAN DEFAULT FALSE COMMENT "System roles cannot be deleted"',
    'SELECT "Column is_system_role already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'user_roles' 
     AND table_schema = 'NeuroIQ' 
     AND column_name = 'max_assignments') = 0,
    'ALTER TABLE user_roles ADD COLUMN max_assignments INT DEFAULT NULL COMMENT "Maximum number of users that can have this role"',
    'SELECT "Column max_assignments already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add indexes to user_roles if they don't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_name = 'user_roles' 
     AND table_schema = 'NeuroIQ' 
     AND index_name = 'idx_role_name') = 0,
    'ALTER TABLE user_roles ADD INDEX idx_role_name (role_name)',
    'SELECT "Index idx_role_name already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_name = 'user_roles' 
     AND table_schema = 'NeuroIQ' 
     AND index_name = 'idx_role_level') = 0,
    'ALTER TABLE user_roles ADD INDEX idx_role_level (role_level)',
    'SELECT "Index idx_role_level already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add new columns to user_role_assignments table if they don't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'user_role_assignments' 
     AND table_schema = 'NeuroIQ' 
     AND column_name = 'assignment_reason') = 0,
    'ALTER TABLE user_role_assignments ADD COLUMN assignment_reason TEXT COMMENT "Reason for role assignment"',
    'SELECT "Column assignment_reason already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'user_role_assignments' 
     AND table_schema = 'NeuroIQ' 
     AND column_name = 'revoked_at') = 0,
    'ALTER TABLE user_role_assignments ADD COLUMN revoked_at TIMESTAMP NULL',
    'SELECT "Column revoked_at already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'user_role_assignments' 
     AND table_schema = 'NeuroIQ' 
     AND column_name = 'revoked_by') = 0,
    'ALTER TABLE user_role_assignments ADD COLUMN revoked_by INT',
    'SELECT "Column revoked_by already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add indexes to user_role_assignments if they don't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_name = 'user_role_assignments' 
     AND table_schema = 'NeuroIQ' 
     AND index_name = 'idx_user_role_active') = 0,
    'ALTER TABLE user_role_assignments ADD INDEX idx_user_role_active (user_id, is_active)',
    'SELECT "Index idx_user_role_active already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ============================================================================
-- STEP 3: CREATE NEW RBAC TABLES
-- ============================================================================

-- Permission Categories Table
CREATE TABLE IF NOT EXISTS permission_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_name VARCHAR(50) UNIQUE NOT NULL,
    category_description TEXT,
    display_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category_name (category_name),
    INDEX idx_display_order (display_order)
);

-- Permissions Table
CREATE TABLE IF NOT EXISTS permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    permission_name VARCHAR(100) UNIQUE NOT NULL,
    permission_description TEXT,
    category_id INT,
    resource_type VARCHAR(50) COMMENT 'Type of resource this permission applies to',
    action_type ENUM('create', 'read', 'update', 'delete', 'execute', 'manage') NOT NULL,
    is_system_permission BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES permission_categories(id) ON DELETE SET NULL,
    INDEX idx_permission_name (permission_name),
    INDEX idx_category (category_id),
    INDEX idx_resource_type (resource_type),
    INDEX idx_action_type (action_type)
);

-- Role Permissions Table
CREATE TABLE IF NOT EXISTS role_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    granted_by INT,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES neuroiq_users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_role_permission (role_id, permission_id),
    INDEX idx_role_permissions (role_id, is_active),
    INDEX idx_permission_roles (permission_id, is_active)
);

-- RBAC Audit Log Table
CREATE TABLE IF NOT EXISTS rbac_audit_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT COMMENT 'User who performed the action',
    target_user_id INT COMMENT 'User affected by the action',
    target_role_id INT COMMENT 'Role affected by the action',
    action_type ENUM('role_created', 'role_updated', 'role_deleted', 'role_assigned', 'role_revoked', 
                     'permission_granted', 'permission_revoked') NOT NULL,
    old_values JSON COMMENT 'Previous values before change',
    new_values JSON COMMENT 'New values after change',
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES neuroiq_users(id) ON DELETE SET NULL,
    FOREIGN KEY (target_user_id) REFERENCES neuroiq_users(id) ON DELETE SET NULL,
    FOREIGN KEY (target_role_id) REFERENCES user_roles(id) ON DELETE SET NULL,
    INDEX idx_user_audit (user_id),
    INDEX idx_target_user_audit (target_user_id),
    INDEX idx_action_type (action_type),
    INDEX idx_audit_date (created_at)
);

-- ============================================================================
-- STEP 4: CREATE RBAC VIEWS
-- ============================================================================

-- User Roles View
CREATE OR REPLACE VIEW v_user_roles AS
SELECT 
    u.id as user_id,
    u.username,
    u.email,
    u.first_name,
    u.last_name,
    r.id as role_id,
    r.role_name,
    r.role_description,
    r.permissions,
    r.role_level,
    ura.assigned_at,
    ura.expires_at,
    ura.is_active as assignment_active,
    CASE 
        WHEN ura.expires_at IS NULL THEN 'permanent'
        WHEN ura.expires_at > NOW() THEN 'active'
        ELSE 'expired'
    END as assignment_status
FROM neuroiq_users u
JOIN user_role_assignments ura ON u.id = ura.user_id
JOIN user_roles r ON ura.role_id = r.id
WHERE u.is_active = TRUE AND r.is_active = TRUE;

-- Role Assignment Summary View
CREATE OR REPLACE VIEW v_role_assignment_summary AS
SELECT 
    r.id as role_id,
    r.role_name,
    r.role_description,
    r.role_level,
    COUNT(ura.user_id) as total_assignments,
    COUNT(CASE WHEN ura.is_active = TRUE THEN 1 END) as active_assignments,
    COUNT(CASE WHEN ura.expires_at IS NOT NULL AND ura.expires_at <= NOW() THEN 1 END) as expired_assignments,
    r.max_assignments,
    CASE 
        WHEN r.max_assignments IS NULL THEN 'unlimited'
        WHEN COUNT(CASE WHEN ura.is_active = TRUE THEN 1 END) >= r.max_assignments THEN 'full'
        ELSE 'available'
    END as assignment_capacity
FROM user_roles r
LEFT JOIN user_role_assignments ura ON r.id = ura.role_id
WHERE r.is_active = TRUE
GROUP BY r.id, r.role_name, r.role_description, r.role_level, r.max_assignments;

-- ============================================================================
-- STEP 5: RESTORE BACKED UP DATA
-- ============================================================================

-- Restore role assignments from backup
INSERT IGNORE INTO user_role_assignments 
SELECT * FROM temp_role_assignments_backup;

-- Clean up backup table
DROP TABLE IF EXISTS temp_role_assignments_backup;

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

SELECT 'RBAC Database Setup Complete!' as Status,
       'Tables created and enhanced successfully' as Message,
       NOW() as Timestamp;
