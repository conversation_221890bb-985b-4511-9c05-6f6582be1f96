-- Initial data for NeuroIQ User Management System
USE NeuroIQ;

-- Insert default roles
INSERT INTO user_roles (role_name, role_description, permissions) VALUES
('system_admin', 'System Administrator with full access', JSON_OBJECT(
    'user_management', true,
    'role_management', true,
    'system_settings', true,
    'audit_access', true,
    'all_permissions', true
)),
('admin', 'Administrator with user management access', JSON_OBJECT(
    'user_management', true,
    'role_assignment', true,
    'user_settings', true,
    'audit_access', true
)),
('manager', 'Manager with limited administrative access', JSON_OBJECT(
    'user_view', true,
    'role_assignment', false,
    'user_settings', false,
    'team_management', true
)),
('user', 'Regular user with basic access', JSON_OBJECT(
    'profile_management', true,
    'settings_management', true,
    'ai_access', true
)),
('guest', 'Guest user with limited access', JSON_OBJECT(
    'profile_view', true,
    'limited_ai_access', true
));

-- Insert default identity providers
INSERT INTO identity_providers (provider_name, provider_type, is_active) VALUES
('local', 'local', true),
('azure_ad', 'oidc', false),
('google', 'oauth2', false),
('facebook', 'oauth2', false);

-- Create default admin user (password: admin123)
-- Note: In production, this should be changed immediately
INSERT INTO neuroiq_users (
    email,
    username,
    password_hash,
    first_name,
    last_name,
    is_active,
    is_verified
) VALUES (
    '<EMAIL>',
    'admin',
    '$2b$12$BcCKSQgmdIQX3nOTbbRPUetg0OlYRoagP987XAQneGL.MkLAWEpT6', -- admin123
    'System',
    'Administrator',
    true,
    true
);

-- Assign admin role to default admin user
INSERT INTO user_role_assignments (user_id, role_id, assigned_by) 
SELECT u.id, r.id, u.id
FROM neuroiq_users u, user_roles r 
WHERE u.username = 'admin' AND r.role_name = 'system_admin';
