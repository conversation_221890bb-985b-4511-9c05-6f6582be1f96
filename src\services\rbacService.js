// RBAC Service
// Handles role-based access control operations

import apiService, { ApiError } from './api';

class RBACService {
  // Role Management
  async getAllRoles(includeInactive = false) {
    try {
      const params = includeInactive ? { include_inactive: true } : {};
      const response = await apiService.get('/api/rbac/roles', params);
      return {
        success: true,
        roles: response
      };
    } catch (error) {
      console.error('Get roles error:', error);
      return {
        success: false,
        error: error.message || 'Failed to fetch roles'
      };
    }
  }

  async getRoleById(roleId) {
    try {
      const response = await apiService.get(`/api/rbac/roles/${roleId}`);
      return {
        success: true,
        role: response
      };
    } catch (error) {
      console.error('Get role error:', error);
      return {
        success: false,
        error: error.message || 'Failed to fetch role'
      };
    }
  }

  async createRole(roleData) {
    try {
      // Transform frontend role data to backend format
      const backendRoleData = {
        role_name: roleData.roleName || roleData.role_name,
        role_description: roleData.description || roleData.role_description,
        permissions: roleData.permissions || {}
      };

      const response = await apiService.post('/api/rbac/roles', backendRoleData);
      return {
        success: true,
        role: response,
        message: 'Role created successfully'
      };
    } catch (error) {
      console.error('Create role error:', error);
      
      let errorMessage = 'Failed to create role';
      if (error instanceof ApiError) {
        if (error.status === 400) {
          errorMessage = error.data?.detail || 'Role with this name already exists';
        } else if (error.status === 403) {
          errorMessage = 'Insufficient permissions to create roles';
        } else {
          errorMessage = error.message;
        }
      }
      
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  async updateRole(roleId, roleData) {
    try {
      const backendRoleData = {
        role_name: roleData.roleName || roleData.role_name,
        role_description: roleData.description || roleData.role_description,
        permissions: roleData.permissions || {}
      };

      const response = await apiService.put(`/api/rbac/roles/${roleId}`, backendRoleData);
      return {
        success: true,
        role: response,
        message: 'Role updated successfully'
      };
    } catch (error) {
      console.error('Update role error:', error);
      return {
        success: false,
        error: error.message || 'Failed to update role'
      };
    }
  }

  async deleteRole(roleId) {
    try {
      await apiService.delete(`/api/rbac/roles/${roleId}`);
      return {
        success: true,
        message: 'Role deleted successfully'
      };
    } catch (error) {
      console.error('Delete role error:', error);
      return {
        success: false,
        error: error.message || 'Failed to delete role'
      };
    }
  }

  // Role Assignment
  async assignRoleToUser(userId, roleId, expiresAt = null) {
    try {
      const data = expiresAt ? { expires_at: expiresAt } : {};
      const response = await apiService.post(`/api/rbac/users/${userId}/roles/${roleId}`, data);
      return {
        success: true,
        assignment: response,
        message: 'Role assigned successfully'
      };
    } catch (error) {
      console.error('Assign role error:', error);
      return {
        success: false,
        error: error.message || 'Failed to assign role'
      };
    }
  }

  async revokeRoleFromUser(userId, roleId) {
    try {
      await apiService.delete(`/api/rbac/users/${userId}/roles/${roleId}`);
      return {
        success: true,
        message: 'Role revoked successfully'
      };
    } catch (error) {
      console.error('Revoke role error:', error);
      return {
        success: false,
        error: error.message || 'Failed to revoke role'
      };
    }
  }

  async getUserRoles(userId) {
    try {
      const response = await apiService.get(`/api/rbac/users/${userId}/roles`);
      return {
        success: true,
        roles: response
      };
    } catch (error) {
      console.error('Get user roles error:', error);
      return {
        success: false,
        error: error.message || 'Failed to fetch user roles'
      };
    }
  }

  async getRoleUsers(roleId) {
    try {
      const response = await apiService.get(`/api/rbac/roles/${roleId}/users`);
      return {
        success: true,
        users: response
      };
    } catch (error) {
      console.error('Get role users error:', error);
      return {
        success: false,
        error: error.message || 'Failed to fetch role users'
      };
    }
  }

  // Permission Management
  async getUserPermissions(userId) {
    try {
      const response = await apiService.get(`/api/rbac/users/${userId}/permissions`);
      return {
        success: true,
        permissions: response.permissions
      };
    } catch (error) {
      console.error('Get user permissions error:', error);
      return {
        success: false,
        error: error.message || 'Failed to fetch user permissions'
      };
    }
  }

  async checkUserPermission(userId, permission) {
    try {
      const response = await apiService.get(`/api/rbac/users/${userId}/permissions/${permission}`);
      return {
        success: true,
        hasPermission: response.has_permission
      };
    } catch (error) {
      console.error('Check permission error:', error);
      return {
        success: false,
        error: error.message || 'Failed to check permission'
      };
    }
  }
}

// Create singleton instance
const rbacService = new RBACService();

export default rbacService;
