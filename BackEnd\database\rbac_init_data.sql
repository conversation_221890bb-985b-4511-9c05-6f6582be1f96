-- NeuroIQ RBAC Initial Data
-- This script populates the RBAC system with default roles, permissions, and categories

USE NeuroIQ;

-- ============================================================================
-- PERMISSION CATEGORIES
-- ============================================================================

INSERT INTO permission_categories (category_name, category_description, display_order) VALUES
('User Management', 'Permissions related to user account management', 1),
('Role Management', 'Permissions related to role and permission management', 2),
('System Administration', 'System-level administrative permissions', 3),
('Data Access', 'Permissions related to data viewing and manipulation', 4),
('Analytics', 'Permissions related to analytics and reporting', 5),
('Security', 'Security and audit-related permissions', 6),
('API Access', 'API and integration permissions', 7);

-- ============================================================================
-- CORE PERMISSIONS
-- ============================================================================

-- User Management Permissions
INSERT INTO permissions (permission_name, permission_description, category_id, resource_type, action_type, is_system_permission) VALUES
('user_create', 'Create new user accounts', 1, 'user', 'create', TRUE),
('user_read', 'View user account information', 1, 'user', 'read', TRUE),
('user_update', 'Update user account information', 1, 'user', 'update', TRUE),
('user_delete', 'Delete or deactivate user accounts', 1, 'user', 'delete', TRUE),
('user_manage', 'Full user management capabilities', 1, 'user', 'manage', TRUE),
('profile_edit', 'Edit own profile information', 1, 'profile', 'update', FALSE),
('profile_view', 'View own profile information', 1, 'profile', 'read', FALSE),

-- Role Management Permissions
('role_create', 'Create new roles', 2, 'role', 'create', TRUE),
('role_read', 'View role information', 2, 'role', 'read', TRUE),
('role_update', 'Update role information and permissions', 2, 'role', 'update', TRUE),
('role_delete', 'Delete roles', 2, 'role', 'delete', TRUE),
('role_assign', 'Assign roles to users', 2, 'role_assignment', 'create', TRUE),
('role_revoke', 'Revoke roles from users', 2, 'role_assignment', 'delete', TRUE),
('permission_manage', 'Manage individual permissions', 2, 'permission', 'manage', TRUE),

-- System Administration Permissions
('system_settings', 'Modify system settings and configuration', 3, 'system', 'manage', TRUE),
('system_maintenance', 'Perform system maintenance tasks', 3, 'system', 'execute', TRUE),
('backup_manage', 'Manage system backups', 3, 'backup', 'manage', TRUE),
('integration_manage', 'Manage external integrations', 3, 'integration', 'manage', TRUE),

-- Data Access Permissions
('data_read', 'Read access to application data', 4, 'data', 'read', FALSE),
('data_write', 'Write access to application data', 4, 'data', 'update', FALSE),
('data_delete', 'Delete application data', 4, 'data', 'delete', FALSE),
('data_export', 'Export application data', 4, 'data', 'execute', FALSE),
('data_import', 'Import application data', 4, 'data', 'create', FALSE),

-- Analytics Permissions
('analytics_view', 'View analytics dashboards', 5, 'analytics', 'read', FALSE),
('analytics_create', 'Create custom analytics', 5, 'analytics', 'create', FALSE),
('reports_view', 'View reports', 5, 'report', 'read', FALSE),
('reports_create', 'Create and modify reports', 5, 'report', 'create', FALSE),
('reports_export', 'Export reports', 5, 'report', 'execute', FALSE),

-- Security Permissions
('audit_view', 'View audit logs and security events', 6, 'audit', 'read', TRUE),
('security_manage', 'Manage security settings', 6, 'security', 'manage', TRUE),
('session_manage', 'Manage user sessions', 6, 'session', 'manage', TRUE),

-- API Access Permissions
('api_read', 'Read access via API', 7, 'api', 'read', FALSE),
('api_write', 'Write access via API', 7, 'api', 'update', FALSE),
('api_admin', 'Administrative API access', 7, 'api', 'manage', TRUE);

-- ============================================================================
-- DEFAULT ROLES WITH ENHANCED PERMISSIONS
-- ============================================================================

-- Update existing roles with enhanced structure
UPDATE user_roles SET 
    role_level = 10, 
    is_system_role = TRUE,
    permissions = JSON_OBJECT(
        'user_manage', true,
        'role_create', true,
        'role_read', true,
        'role_update', true,
        'role_delete', true,
        'role_assign', true,
        'role_revoke', true,
        'permission_manage', true,
        'system_settings', true,
        'system_maintenance', true,
        'backup_manage', true,
        'integration_manage', true,
        'audit_view', true,
        'security_manage', true,
        'session_manage', true,
        'api_admin', true,
        'all_permissions', true
    )
WHERE role_name = 'system_admin';

UPDATE user_roles SET 
    role_level = 8, 
    is_system_role = TRUE,
    permissions = JSON_OBJECT(
        'user_create', true,
        'user_read', true,
        'user_update', true,
        'user_delete', true,
        'role_read', true,
        'role_assign', true,
        'role_revoke', true,
        'data_read', true,
        'data_write', true,
        'data_export', true,
        'analytics_view', true,
        'reports_view', true,
        'reports_create', true,
        'audit_view', true,
        'session_manage', true,
        'api_read', true,
        'api_write', true
    )
WHERE role_name = 'admin';

UPDATE user_roles SET 
    role_level = 5, 
    is_system_role = TRUE,
    permissions = JSON_OBJECT(
        'user_read', true,
        'role_read', true,
        'role_assign', false,
        'data_read', true,
        'data_write', false,
        'analytics_view', true,
        'reports_view', true,
        'team_management', true,
        'api_read', true
    )
WHERE role_name = 'manager';

UPDATE user_roles SET 
    role_level = 2, 
    is_system_role = TRUE,
    permissions = JSON_OBJECT(
        'profile_view', true,
        'profile_edit', true,
        'data_read', true,
        'analytics_view', false,
        'reports_view', false,
        'api_read', true
    )
WHERE role_name = 'user';

UPDATE user_roles SET 
    role_level = 1, 
    is_system_role = TRUE,
    permissions = JSON_OBJECT(
        'profile_view', true,
        'data_read', false,
        'analytics_view', false,
        'reports_view', false
    )
WHERE role_name = 'guest';

-- ============================================================================
-- ADDITIONAL SPECIALIZED ROLES
-- ============================================================================

INSERT INTO user_roles (role_name, role_description, permissions, role_level, is_system_role) VALUES

-- Data Analyst Role
('data_analyst', 'Data analyst with comprehensive analytics access', JSON_OBJECT(
    'profile_view', true,
    'profile_edit', true,
    'data_read', true,
    'data_export', true,
    'analytics_view', true,
    'analytics_create', true,
    'reports_view', true,
    'reports_create', true,
    'reports_export', true,
    'api_read', true
), 4, FALSE),

-- Content Manager Role
('content_manager', 'Content manager with data management permissions', JSON_OBJECT(
    'profile_view', true,
    'profile_edit', true,
    'data_read', true,
    'data_write', true,
    'data_import', true,
    'data_export', true,
    'reports_view', true,
    'api_read', true,
    'api_write', true
), 4, FALSE),

-- Security Officer Role
('security_officer', 'Security officer with audit and security permissions', JSON_OBJECT(
    'profile_view', true,
    'user_read', true,
    'role_read', true,
    'audit_view', true,
    'security_manage', true,
    'session_manage', true,
    'data_read', true,
    'reports_view', true,
    'api_read', true
), 6, FALSE),

-- API User Role
('api_user', 'API-only access for external integrations', JSON_OBJECT(
    'api_read', true,
    'api_write', true,
    'data_read', true,
    'data_write', true
), 3, FALSE),

-- Read-Only Role
('read_only', 'Read-only access to most system features', JSON_OBJECT(
    'profile_view', true,
    'user_read', true,
    'role_read', true,
    'data_read', true,
    'analytics_view', true,
    'reports_view', true,
    'api_read', true
), 2, FALSE),

-- Team Lead Role
('team_lead', 'Team leader with limited management capabilities', JSON_OBJECT(
    'profile_view', true,
    'profile_edit', true,
    'user_read', true,
    'role_read', true,
    'role_assign', true,
    'data_read', true,
    'data_write', true,
    'analytics_view', true,
    'reports_view', true,
    'reports_create', true,
    'team_management', true,
    'api_read', true
), 5, FALSE);

-- ============================================================================
-- ROLE HIERARCHY SETUP
-- ============================================================================

-- Set up role inheritance (higher roles inherit permissions from lower roles)
INSERT INTO role_hierarchy (parent_role_id, child_role_id, inheritance_type, created_by) VALUES
-- System Admin inherits from Admin
((SELECT id FROM user_roles WHERE role_name = 'system_admin'), 
 (SELECT id FROM user_roles WHERE role_name = 'admin'), 'additive', 1),

-- Admin inherits from Manager
((SELECT id FROM user_roles WHERE role_name = 'admin'), 
 (SELECT id FROM user_roles WHERE role_name = 'manager'), 'additive', 1),

-- Manager inherits from User
((SELECT id FROM user_roles WHERE role_name = 'manager'), 
 (SELECT id FROM user_roles WHERE role_name = 'user'), 'additive', 1),

-- Team Lead inherits from User
((SELECT id FROM user_roles WHERE role_name = 'team_lead'), 
 (SELECT id FROM user_roles WHERE role_name = 'user'), 'additive', 1),

-- Data Analyst inherits from User
((SELECT id FROM user_roles WHERE role_name = 'data_analyst'), 
 (SELECT id FROM user_roles WHERE role_name = 'user'), 'additive', 1),

-- Content Manager inherits from User
((SELECT id FROM user_roles WHERE role_name = 'content_manager'), 
 (SELECT id FROM user_roles WHERE role_name = 'user'), 'additive', 1);

-- ============================================================================
-- ROLE-PERMISSION MAPPINGS
-- ============================================================================

-- Map permissions to roles (this provides granular control beyond JSON permissions)
-- System Admin gets all permissions
INSERT INTO role_permissions (role_id, permission_id, granted_by)
SELECT 
    (SELECT id FROM user_roles WHERE role_name = 'system_admin'),
    p.id,
    1
FROM permissions p;

-- Admin gets most permissions except system-level ones
INSERT INTO role_permissions (role_id, permission_id, granted_by)
SELECT 
    (SELECT id FROM user_roles WHERE role_name = 'admin'),
    p.id,
    1
FROM permissions p
WHERE p.permission_name NOT IN ('system_settings', 'system_maintenance', 'backup_manage', 'permission_manage');

-- Manager gets read permissions and team management
INSERT INTO role_permissions (role_id, permission_id, granted_by)
SELECT 
    (SELECT id FROM user_roles WHERE role_name = 'manager'),
    p.id,
    1
FROM permissions p
WHERE p.permission_name IN ('user_read', 'role_read', 'role_assign', 'data_read', 'analytics_view', 'reports_view', 'api_read');

-- User gets basic permissions
INSERT INTO role_permissions (role_id, permission_id, granted_by)
SELECT 
    (SELECT id FROM user_roles WHERE role_name = 'user'),
    p.id,
    1
FROM permissions p
WHERE p.permission_name IN ('profile_view', 'profile_edit', 'data_read', 'api_read');
