from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Optional, List

from ..models.user import (
    UserResponse, UserUpdate, UserPasswordUpdate, UserDetailResponse,
    UserListResponse, UserSearchParams
)
from ..models.auth import CurrentUser
from ..services.user_service import UserService
from ..services.auth_service import AuthService
from .auth import get_current_user_dependency

router = APIRouter(prefix="/users", tags=["Users"])

# Initialize services
user_service = UserService()
auth_service = AuthService()

@router.get("/me", response_model=UserDetailResponse)
async def get_my_profile(current_user: CurrentUser = Depends(get_current_user_dependency)):
    """Get current user's profile"""
    try:
        user = await user_service.get_user_by_id(current_user.id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Get permissions
        permissions = await auth_service.get_user_permissions(current_user.id)
        
        # Convert to detailed response
        user_detail = UserDetailResponse(
            **user.dict(),
            permissions=permissions,
            external_identities=[],  # TODO: Implement external identities
            last_activity=user.last_login
        )
        
        return user_detail
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user profile"
        )

@router.put("/me", response_model=UserResponse)
async def update_my_profile(
    user_data: UserUpdate,
    current_user: CurrentUser = Depends(get_current_user_dependency)
):
    """Update current user's profile"""
    try:
        return await user_service.update_user(current_user.id, user_data)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user profile"
        )

@router.put("/me/password")
async def update_my_password(
    password_data: UserPasswordUpdate,
    current_user: CurrentUser = Depends(get_current_user_dependency)
):
    """Update current user's password"""
    try:
        success = await user_service.update_password(current_user.id, password_data)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update password"
            )
        
        return {"message": "Password updated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update password"
        )

@router.get("/{user_id}", response_model=UserDetailResponse)
async def get_user_by_id(
    user_id: int,
    current_user: CurrentUser = Depends(get_current_user_dependency)
):
    """Get user by ID (requires admin permissions)"""
    try:
        # Check permissions
        if not current_user.permissions.get("user_management", False) and current_user.id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        
        user = await user_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Get permissions
        permissions = await auth_service.get_user_permissions(user_id)
        
        # Convert to detailed response
        user_detail = UserDetailResponse(
            **user.dict(),
            permissions=permissions,
            external_identities=[],  # TODO: Implement external identities
            last_activity=user.last_login
        )
        
        return user_detail
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user"
        )

@router.put("/{user_id}", response_model=UserResponse)
async def update_user_by_id(
    user_id: int,
    user_data: UserUpdate,
    current_user: CurrentUser = Depends(get_current_user_dependency)
):
    """Update user by ID (requires admin permissions)"""
    try:
        # Check permissions
        if not current_user.permissions.get("user_management", False) and current_user.id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        
        return await user_service.update_user(user_id, user_data)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user"
        )

@router.get("/", response_model=List[UserResponse])
async def get_users(
    search: Optional[str] = Query(None, description="Search by username, email, or name"),
    role: Optional[str] = Query(None, description="Filter by role"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    is_verified: Optional[bool] = Query(None, description="Filter by verification status"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(10, ge=1, le=100, description="Page size"),
    current_user: CurrentUser = Depends(get_current_user_dependency)
):
    """Get list of users (requires admin permissions)"""
    try:
        # Check permissions
        if not current_user.permissions.get("user_management", False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        
        # TODO: Implement user search and pagination
        # For now, return empty list
        return []
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get users"
        )

@router.delete("/{user_id}")
async def deactivate_user(
    user_id: int,
    current_user: CurrentUser = Depends(get_current_user_dependency)
):
    """Deactivate user (soft delete)"""
    try:
        # Check permissions
        if not current_user.permissions.get("user_management", False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        
        # Prevent self-deactivation
        if current_user.id == user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot deactivate your own account"
            )
        
        # TODO: Implement user deactivation
        # For now, return success message
        
        return {"message": "User deactivated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to deactivate user"
        )
