@echo off
echo NeuroIQ User Management API Setup
echo ================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

echo Python found!

REM Create virtual environment if it doesn't exist
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Install requirements
echo Installing requirements...
pip install -r requirements.txt

REM Check if .env file exists
if not exist ".env" (
    echo Error: .env file not found!
    echo Please create a .env file with your database configuration
    pause
    exit /b 1
)

REM Setup database
echo Setting up database...
python setup_database.py

if errorlevel 1 (
    echo Database setup failed!
    pause
    exit /b 1
)

echo.
echo ================================
echo Setup completed successfully!
echo.
echo Starting the API server...
echo API will be available at: http://localhost:8000
echo API Documentation: http://localhost:8000/docs
echo.
echo Press Ctrl+C to stop the server
echo ================================
echo.

REM Start the server
python run.py

pause
