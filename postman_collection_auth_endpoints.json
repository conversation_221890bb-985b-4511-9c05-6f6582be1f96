{"info": {"name": "R-NeuroIQ Auth API", "description": "Authentication endpoints for R-NeuroIQ Admin Portal", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}], "item": [{"name": "1. Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"username\": \"johndo<PERSON>\",\n  \"password\": \"SecurePass123!\",\n  \"confirm_password\": \"SecurePass123!\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"phone\": \"+1234567890\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/register", "host": ["{{base_url}}"], "path": ["api", "auth", "register"]}}}, {"name": "2. <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"johndoe\",\n  \"password\": \"SecurePass123!\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('access_token', response.tokens.access_token);", "    pm.environment.set('refresh_token', response.tokens.refresh_token);", "}"]}}]}, {"name": "3. <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refresh_token\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/refresh", "host": ["{{base_url}}"], "path": ["api", "auth", "refresh"]}}}, {"name": "4. Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/auth/me", "host": ["{{base_url}}"], "path": ["api", "auth", "me"]}}}, {"name": "5. <PERSON><PERSON><PERSON> User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/auth/logout", "host": ["{{base_url}}"], "path": ["api", "auth", "logout"]}}}, {"name": "6. Request Password Reset", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/password-reset/request", "host": ["{{base_url}}"], "path": ["api", "auth", "password-reset", "request"]}}}, {"name": "7. Confirm Password Reset", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"reset_token_from_email\",\n  \"new_password\": \"NewSecurePass789!\",\n  \"confirm_password\": \"NewSecurePass789!\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/password-reset/confirm", "host": ["{{base_url}}"], "path": ["api", "auth", "password-reset", "confirm"]}}}, {"name": "8. Request Email Verification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/email-verification/request", "host": ["{{base_url}}"], "path": ["api", "auth", "email-verification", "request"]}}}, {"name": "9. Confirm Email Verification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"verification_token_from_email\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/email-verification/confirm", "host": ["{{base_url}}"], "path": ["api", "auth", "email-verification", "confirm"]}}}]}