-- NeuroIQ RBAC (Role-Based Access Control) Enhanced Schema
-- This script enhances the existing schema with RBAC-specific optimizations and additional tables

USE NeuroIQ;

-- ============================================================================
-- RBAC CORE TABLES ENHANCEMENTS
-- ============================================================================

-- Enhanced User Roles Table with additional constraints and indexes
ALTER TABLE user_roles 
ADD COLUMN IF NOT EXISTS created_by INT,
ADD COLUMN IF NOT EXISTS updated_by INT,
ADD COLUMN IF NOT EXISTS role_level INT DEFAULT 1 COMMENT 'Role hierarchy level (1=lowest, 10=highest)',
ADD COLUMN IF NOT EXISTS is_system_role BOOLEAN DEFAULT FALSE COMMENT 'System roles cannot be deleted',
ADD COLUMN IF NOT EXISTS max_assignments INT DEFAULT NULL COMMENT 'Maximum number of users that can have this role',
ADD INDEX idx_role_name (role_name),
ADD INDEX idx_role_active (is_active),
ADD INDEX idx_role_level (role_level),
ADD INDEX idx_system_role (is_system_role),
ADD FOREIGN KEY fk_role_created_by (created_by) REFERENCES neuroiq_users(id) ON DELETE SET NULL,
ADD FOREIGN KEY fk_role_updated_by (updated_by) REFERENCES neuroiq_users(id) ON DELETE SET NULL;

-- Enhanced User Role Assignments Table with additional tracking
ALTER TABLE user_role_assignments 
ADD COLUMN IF NOT EXISTS assignment_reason TEXT COMMENT 'Reason for role assignment',
ADD COLUMN IF NOT EXISTS revoked_at TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS revoked_by INT,
ADD COLUMN IF NOT EXISTS revocation_reason TEXT,
ADD INDEX idx_user_role_active (user_id, is_active),
ADD INDEX idx_role_user_active (role_id, is_active),
ADD INDEX idx_assigned_by (assigned_by),
ADD INDEX idx_assignment_date (assigned_at),
ADD INDEX idx_expiry_date (expires_at),
ADD INDEX idx_revoked_by (revoked_by),
ADD FOREIGN KEY fk_assignment_revoked_by (revoked_by) REFERENCES neuroiq_users(id) ON DELETE SET NULL;

-- ============================================================================
-- RBAC ADDITIONAL TABLES
-- ============================================================================

-- 1. Permission Categories Table
CREATE TABLE IF NOT EXISTS permission_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_name VARCHAR(50) UNIQUE NOT NULL,
    category_description TEXT,
    display_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category_name (category_name),
    INDEX idx_display_order (display_order)
);

-- 2. Permissions Table (for granular permission management)
CREATE TABLE IF NOT EXISTS permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    permission_name VARCHAR(100) UNIQUE NOT NULL,
    permission_description TEXT,
    category_id INT,
    resource_type VARCHAR(50) COMMENT 'Type of resource this permission applies to',
    action_type ENUM('create', 'read', 'update', 'delete', 'execute', 'manage') NOT NULL,
    is_system_permission BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES permission_categories(id) ON DELETE SET NULL,
    INDEX idx_permission_name (permission_name),
    INDEX idx_category (category_id),
    INDEX idx_resource_type (resource_type),
    INDEX idx_action_type (action_type),
    INDEX idx_system_permission (is_system_permission)
);

-- 3. Role Permissions Table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS role_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    granted_by INT,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES neuroiq_users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_role_permission (role_id, permission_id),
    INDEX idx_role_permissions (role_id, is_active),
    INDEX idx_permission_roles (permission_id, is_active),
    INDEX idx_granted_by (granted_by)
);

-- 4. Role Hierarchy Table (for role inheritance)
CREATE TABLE IF NOT EXISTS role_hierarchy (
    id INT AUTO_INCREMENT PRIMARY KEY,
    parent_role_id INT NOT NULL,
    child_role_id INT NOT NULL,
    inheritance_type ENUM('full', 'partial', 'additive') DEFAULT 'full',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (parent_role_id) REFERENCES user_roles(id) ON DELETE CASCADE,
    FOREIGN KEY (child_role_id) REFERENCES user_roles(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES neuroiq_users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_role_hierarchy (parent_role_id, child_role_id),
    INDEX idx_parent_role (parent_role_id),
    INDEX idx_child_role (child_role_id),
    CHECK (parent_role_id != child_role_id)
);

-- 5. User Permission Overrides Table (for user-specific permission grants/denials)
CREATE TABLE IF NOT EXISTS user_permission_overrides (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    permission_id INT NOT NULL,
    override_type ENUM('grant', 'deny') NOT NULL,
    override_reason TEXT,
    granted_by INT,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES neuroiq_users(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES neuroiq_users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_user_permission_override (user_id, permission_id),
    INDEX idx_user_overrides (user_id, is_active),
    INDEX idx_permission_overrides (permission_id, is_active),
    INDEX idx_override_type (override_type),
    INDEX idx_granted_by (granted_by)
);

-- 6. RBAC Audit Log Table (for tracking all RBAC changes)
CREATE TABLE IF NOT EXISTS rbac_audit_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT COMMENT 'User who performed the action',
    target_user_id INT COMMENT 'User affected by the action',
    target_role_id INT COMMENT 'Role affected by the action',
    target_permission_id INT COMMENT 'Permission affected by the action',
    action_type ENUM('role_created', 'role_updated', 'role_deleted', 'role_assigned', 'role_revoked', 
                     'permission_granted', 'permission_revoked', 'hierarchy_created', 'hierarchy_removed') NOT NULL,
    old_values JSON COMMENT 'Previous values before change',
    new_values JSON COMMENT 'New values after change',
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES neuroiq_users(id) ON DELETE SET NULL,
    FOREIGN KEY (target_user_id) REFERENCES neuroiq_users(id) ON DELETE SET NULL,
    FOREIGN KEY (target_role_id) REFERENCES user_roles(id) ON DELETE SET NULL,
    FOREIGN KEY (target_permission_id) REFERENCES permissions(id) ON DELETE SET NULL,
    INDEX idx_user_audit (user_id),
    INDEX idx_target_user_audit (target_user_id),
    INDEX idx_target_role_audit (target_role_id),
    INDEX idx_action_type (action_type),
    INDEX idx_audit_date (created_at)
);

-- ============================================================================
-- RBAC VIEWS FOR EASY QUERYING
-- ============================================================================

-- View: User Roles with Details
CREATE OR REPLACE VIEW v_user_roles AS
SELECT 
    u.id as user_id,
    u.username,
    u.email,
    u.first_name,
    u.last_name,
    r.id as role_id,
    r.role_name,
    r.role_description,
    r.permissions,
    r.role_level,
    ura.assigned_at,
    ura.expires_at,
    ura.is_active as assignment_active,
    CASE 
        WHEN ura.expires_at IS NULL THEN 'permanent'
        WHEN ura.expires_at > NOW() THEN 'active'
        ELSE 'expired'
    END as assignment_status
FROM neuroiq_users u
JOIN user_role_assignments ura ON u.id = ura.user_id
JOIN user_roles r ON ura.role_id = r.id
WHERE u.is_active = TRUE AND r.is_active = TRUE;

-- View: Role Assignment Summary
CREATE OR REPLACE VIEW v_role_assignment_summary AS
SELECT 
    r.id as role_id,
    r.role_name,
    r.role_description,
    r.role_level,
    COUNT(ura.user_id) as total_assignments,
    COUNT(CASE WHEN ura.is_active = TRUE THEN 1 END) as active_assignments,
    COUNT(CASE WHEN ura.expires_at IS NOT NULL AND ura.expires_at <= NOW() THEN 1 END) as expired_assignments,
    r.max_assignments,
    CASE 
        WHEN r.max_assignments IS NULL THEN 'unlimited'
        WHEN COUNT(CASE WHEN ura.is_active = TRUE THEN 1 END) >= r.max_assignments THEN 'full'
        ELSE 'available'
    END as assignment_capacity
FROM user_roles r
LEFT JOIN user_role_assignments ura ON r.id = ura.role_id
WHERE r.is_active = TRUE
GROUP BY r.id, r.role_name, r.role_description, r.role_level, r.max_assignments;

-- View: User Effective Permissions (combining role permissions and overrides)
CREATE OR REPLACE VIEW v_user_effective_permissions AS
SELECT DISTINCT
    u.id as user_id,
    u.username,
    p.id as permission_id,
    p.permission_name,
    p.resource_type,
    p.action_type,
    CASE 
        WHEN upo.override_type = 'deny' THEN FALSE
        WHEN upo.override_type = 'grant' THEN TRUE
        WHEN rp.id IS NOT NULL THEN TRUE
        ELSE FALSE
    END as has_permission,
    CASE 
        WHEN upo.override_type IS NOT NULL THEN 'override'
        WHEN rp.id IS NOT NULL THEN 'role'
        ELSE 'none'
    END as permission_source
FROM neuroiq_users u
CROSS JOIN permissions p
LEFT JOIN user_role_assignments ura ON u.id = ura.user_id AND ura.is_active = TRUE
LEFT JOIN role_permissions rp ON ura.role_id = rp.role_id AND rp.permission_id = p.id AND rp.is_active = TRUE
LEFT JOIN user_permission_overrides upo ON u.id = upo.user_id AND upo.permission_id = p.id AND upo.is_active = TRUE
    AND (upo.expires_at IS NULL OR upo.expires_at > NOW())
WHERE u.is_active = TRUE AND p.is_active = TRUE;
