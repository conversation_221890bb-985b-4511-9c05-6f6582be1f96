import mysql.connector
from mysql.connector import pooling
from typing import Optional, Dict, Any, List
import logging
from .settings import settings

logger = logging.getLogger(__name__)

class DatabaseManager:
    def __init__(self):
        self.pool_config = {
            'pool_name': 'neuroiq_pool',
            'pool_size': 10,
            'pool_reset_session': True,
            'host': settings.db_host,
            'port': settings.db_port,
            'user': settings.db_user,
            'password': settings.db_password,
            'database': settings.db_name,
            'charset': 'utf8mb4',
            'collation': 'utf8mb4_unicode_ci',
            'autocommit': True,
            'time_zone': '+00:00'
        }
        self.pool = None
        self._initialize_pool()
    
    def _initialize_pool(self):
        """Initialize the connection pool"""
        try:
            self.pool = mysql.connector.pooling.MySQLConnectionPool(**self.pool_config)
            logger.info("Database connection pool initialized successfully")
        except mysql.connector.Error as err:
            logger.error(f"Error creating connection pool: {err}")
            raise
    
    def get_connection(self):
        """Get a connection from the pool"""
        try:
            return self.pool.get_connection()
        except mysql.connector.Error as err:
            logger.error(f"Error getting connection from pool: {err}")
            raise
    
    async def execute_query(self, query: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """Execute a SELECT query and return results"""
        connection = None
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)
            cursor.execute(query, params or ())
            result = cursor.fetchall()
            cursor.close()
            return result
        except mysql.connector.Error as err:
            logger.error(f"Error executing query: {err}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    async def execute_update(self, query: str, params: Optional[tuple] = None) -> int:
        """Execute an INSERT, UPDATE, or DELETE query and return affected rows"""
        connection = None
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            cursor.execute(query, params or ())
            affected_rows = cursor.rowcount
            connection.commit()
            cursor.close()
            return affected_rows
        except mysql.connector.Error as err:
            logger.error(f"Error executing update: {err}")
            if connection:
                connection.rollback()
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    async def execute_insert(self, query: str, params: Optional[tuple] = None) -> int:
        """Execute an INSERT query and return the last inserted ID"""
        connection = None
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            cursor.execute(query, params or ())
            last_id = cursor.lastrowid
            connection.commit()
            cursor.close()
            return last_id
        except mysql.connector.Error as err:
            logger.error(f"Error executing insert: {err}")
            if connection:
                connection.rollback()
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    def close_pool(self):
        """Close all connections in the pool"""
        if self.pool:
            # Note: mysql.connector doesn't have a direct method to close the pool
            # Connections will be closed automatically when they go out of scope
            logger.info("Database connection pool closed")

# Global database manager instance
db_manager = DatabaseManager()
