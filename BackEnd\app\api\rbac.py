from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Optional, List
from datetime import datetime

from ..models.user import (
    RoleCreate, RoleUpdate, RoleResponse,
    UserRoleAssignment, UserRoleAssignmentResponse
)
from ..models.auth import CurrentUser
from ..services.rbac_service import RBACService
from ..utils.permissions import (
    Permission<PERSON><PERSON><PERSON>, get_permission_checker,
    require_role_management_dependency,
    require_user_management_dependency,
    require_role_assignment_dependency
)
from .auth import get_current_user_dependency

router = APIRouter(prefix="/rbac", tags=["Role-Based Access Control"])

# Initialize service
rbac_service = RBACService()

# Permission checking helper
def check_role_management_permission(current_user: CurrentUser):
    """Check if user has role management permissions"""
    if not current_user.permissions.get("role_management", False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions for role management"
        )

def check_role_assignment_permission(current_user: CurrentUser):
    """Check if user has role assignment permissions"""
    if not (current_user.permissions.get("role_management", False) or 
            current_user.permissions.get("role_assignment", False)):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions for role assignment"
        )

# Role Management Endpoints
@router.post("/roles", response_model=RoleResponse, status_code=status.HTTP_201_CREATED)
async def create_role(
    role_data: RoleCreate,
    current_user: CurrentUser = Depends(require_role_management_dependency)
):
    """Create a new role (requires role_management permission)"""
    try:
        return await rbac_service.create_role(role_data)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create role"
        )

@router.get("/roles", response_model=List[RoleResponse])
async def get_all_roles(
    include_inactive: bool = Query(False, description="Include inactive roles"),
    current_user: CurrentUser = Depends(get_current_user_dependency)
):
    """Get all roles (requires role_management or role_assignment permission)"""
    try:
        # Check permissions - allow both role_management and role_assignment users to view roles
        if not (current_user.permissions.get("role_management", False) or 
                current_user.permissions.get("role_assignment", False) or
                current_user.permissions.get("user_management", False)):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view roles"
            )
        
        return await rbac_service.get_all_roles(include_inactive)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get roles"
        )

@router.get("/roles/{role_id}", response_model=RoleResponse)
async def get_role_by_id(
    role_id: int,
    current_user: CurrentUser = Depends(get_current_user_dependency)
):
    """Get role by ID (requires role_management or role_assignment permission)"""
    try:
        # Check permissions
        if not (current_user.permissions.get("role_management", False) or 
                current_user.permissions.get("role_assignment", False) or
                current_user.permissions.get("user_management", False)):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view role"
            )
        
        role = await rbac_service.get_role_by_id(role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        
        return role
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get role"
        )

@router.put("/roles/{role_id}", response_model=RoleResponse)
async def update_role(
    role_id: int,
    role_data: RoleUpdate,
    current_user: CurrentUser = Depends(require_role_management_dependency)
):
    """Update role (requires role_management permission)"""
    try:
        updated_role = await rbac_service.update_role(role_id, role_data)
        if not updated_role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )

        return updated_role

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update role"
        )

@router.delete("/roles/{role_id}")
async def delete_role(
    role_id: int,
    current_user: CurrentUser = Depends(require_role_management_dependency)
):
    """Delete role (soft delete - requires role_management permission)"""
    try:
        success = await rbac_service.delete_role(role_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found or cannot be deleted"
            )

        return {"message": "Role deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete role"
        )

# Role Assignment Endpoints
@router.post("/users/{user_id}/roles/{role_id}", response_model=UserRoleAssignmentResponse, status_code=status.HTTP_201_CREATED)
async def assign_role_to_user(
    user_id: int,
    role_id: int,
    expires_at: Optional[datetime] = None,
    current_user: CurrentUser = Depends(get_current_user_dependency)
):
    """Assign role to user (requires role_assignment or role_management permission)"""
    try:
        check_role_assignment_permission(current_user)
        
        return await rbac_service.assign_role_to_user(
            user_id, role_id, current_user.id, expires_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to assign role"
        )

@router.delete("/users/{user_id}/roles/{role_id}")
async def remove_role_from_user(
    user_id: int,
    role_id: int,
    current_user: CurrentUser = Depends(get_current_user_dependency)
):
    """Remove role from user (requires role_assignment or role_management permission)"""
    try:
        check_role_assignment_permission(current_user)
        
        success = await rbac_service.remove_role_from_user(user_id, role_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role assignment not found"
            )
        
        return {"message": "Role removed from user successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove role"
        )

@router.get("/users/{user_id}/roles", response_model=List[UserRoleAssignmentResponse])
async def get_user_roles(
    user_id: int,
    current_user: CurrentUser = Depends(get_current_user_dependency)
):
    """Get all roles assigned to a user"""
    try:
        # Allow users to view their own roles, or require user_management permission
        if current_user.id != user_id and not current_user.permissions.get("user_management", False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view user roles"
            )
        
        return await rbac_service.get_user_roles(user_id)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user roles"
        )

@router.get("/roles/{role_id}/users", response_model=List[UserRoleAssignmentResponse])
async def get_role_users(
    role_id: int,
    current_user: CurrentUser = Depends(get_current_user_dependency)
):
    """Get all users assigned to a role (requires user_management permission)"""
    try:
        if not current_user.permissions.get("user_management", False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view role assignments"
            )
        
        return await rbac_service.get_role_users(role_id)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get role users"
        )

# Permission Checking Endpoints
@router.get("/users/{user_id}/permissions")
async def get_user_permissions(
    user_id: int,
    current_user: CurrentUser = Depends(get_current_user_dependency)
):
    """Get all permissions for a user"""
    try:
        # Allow users to view their own permissions, or require user_management permission
        if current_user.id != user_id and not current_user.permissions.get("user_management", False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view user permissions"
            )

        permissions = await rbac_service.get_user_permissions(user_id)
        return {"user_id": user_id, "permissions": permissions}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user permissions"
        )

@router.get("/users/{user_id}/permissions/{permission}")
async def check_user_permission(
    user_id: int,
    permission: str,
    current_user: CurrentUser = Depends(get_current_user_dependency)
):
    """Check if user has a specific permission"""
    try:
        # Allow users to check their own permissions, or require user_management permission
        if current_user.id != user_id and not current_user.permissions.get("user_management", False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to check user permissions"
            )

        has_permission = await rbac_service.check_user_permission(user_id, permission)
        return {
            "user_id": user_id,
            "permission": permission,
            "has_permission": has_permission
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check user permission"
        )

# Bulk Operations
@router.post("/users/{user_id}/roles/bulk")
async def assign_multiple_roles_to_user(
    user_id: int,
    role_ids: List[int],
    current_user: CurrentUser = Depends(get_current_user_dependency)
):
    """Assign multiple roles to a user (requires role_assignment or role_management permission)"""
    try:
        check_role_assignment_permission(current_user)

        assignments = []
        errors = []

        for role_id in role_ids:
            try:
                assignment = await rbac_service.assign_role_to_user(
                    user_id, role_id, current_user.id
                )
                assignments.append(assignment)
            except HTTPException as e:
                errors.append({"role_id": role_id, "error": e.detail})
            except Exception as e:
                errors.append({"role_id": role_id, "error": str(e)})

        return {
            "user_id": user_id,
            "successful_assignments": assignments,
            "errors": errors
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to assign roles"
        )

@router.delete("/users/{user_id}/roles/bulk")
async def remove_multiple_roles_from_user(
    user_id: int,
    role_ids: List[int],
    current_user: CurrentUser = Depends(get_current_user_dependency)
):
    """Remove multiple roles from a user (requires role_assignment or role_management permission)"""
    try:
        check_role_assignment_permission(current_user)

        successful_removals = []
        errors = []

        for role_id in role_ids:
            try:
                success = await rbac_service.remove_role_from_user(user_id, role_id)
                if success:
                    successful_removals.append(role_id)
                else:
                    errors.append({"role_id": role_id, "error": "Role assignment not found"})
            except HTTPException as e:
                errors.append({"role_id": role_id, "error": e.detail})
            except Exception as e:
                errors.append({"role_id": role_id, "error": str(e)})

        return {
            "user_id": user_id,
            "successful_removals": successful_removals,
            "errors": errors
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove roles"
        )
