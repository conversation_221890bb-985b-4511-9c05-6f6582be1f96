import React, { useState, useEffect } from 'react';

const RoleDetails = ({ role, isEditing, onEdit, onSave, onCancel, onDelete }) => {
  const [formData, setFormData] = useState(role);

  useEffect(() => {
    setFormData(role);
  }, [role]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <div className="role-details">
      <h3>{role.name} Role</h3>
      
      {isEditing ? (
        <form onSubmit={handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <div className="form-group">
              <label className="form-label">Role Name</label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className="form-input"
              />
            </div>
            <div className="form-group">
              <label className="form-label">Role Type</label>
              <select
                name="type"
                value={formData.type}
                onChange={handleChange}
                className="form-input"
              >
                <option value="System Role">System Role</option>
                <option value="Custom Role">Custom Role</option>
                <option value="Department Role">Department Role</option>
              </select>
            </div>
          </div>
          
          <div className="form-group">
            <label className="form-label">Description</label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              className="form-input"
              rows="2"
              placeholder="Detailed explanation of role purpose"
            />
          </div>
          
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '16px' }}>
            <div className="form-group">
              <label className="form-label">Status</label>
              <select
                name="status"
                value={formData.status}
                onChange={handleChange}
                className="form-input"
              >
                <option value="Active">Active</option>
                <option value="Inactive">Inactive</option>
                <option value="Deprecated">Deprecated</option>
              </select>
            </div>
            <div className="form-group">
              <label className="form-label">User Count</label>
              <input
                type="text"
                value={formData.userCount}
                className="form-input"
                readOnly
                style={{ background: '#f9fafb' }}
              />
            </div>
            <div className="form-group">
              <label className="form-label">Default Role</label>
              <select
                name="isDefault"
                value={formData.isDefault ? 'Yes' : 'No'}
                onChange={(e) => handleChange({
                  target: { name: 'isDefault', value: e.target.value === 'Yes', type: 'checkbox', checked: e.target.value === 'Yes' }
                })}
                className="form-input"
              >
                <option value="No">No</option>
                <option value="Yes">Yes</option>
              </select>
            </div>
          </div>

          <div className="actions-bar" style={{ marginTop: '20px' }}>
            <div>
              <button type="button" className="btn-secondary" onClick={onCancel}>
                Cancel
              </button>
              <button type="submit" className="btn-primary">
                Save Changes
              </button>
            </div>
          </div>
        </form>
      ) : (
        <div>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <div className="form-group">
              <label className="form-label">Role Name</label>
              <input
                type="text"
                value={role.name}
                className="form-input"
                readOnly
                style={{ background: '#f9fafb' }}
              />
            </div>
            <div className="form-group">
              <label className="form-label">Role Type</label>
              <input
                type="text"
                value={role.type}
                className="form-input"
                readOnly
                style={{ background: '#f9fafb' }}
              />
            </div>
          </div>
          
          <div className="form-group">
            <label className="form-label">Description</label>
            <textarea
              value={role.description}
              className="form-input"
              rows="2"
              readOnly
              style={{ background: '#f9fafb' }}
            />
          </div>
          
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '16px' }}>
            <div className="form-group">
              <label className="form-label">Status</label>
              <input
                type="text"
                value={role.status}
                className="form-input"
                readOnly
                style={{ background: '#f9fafb' }}
              />
            </div>
            <div className="form-group">
              <label className="form-label">User Count</label>
              <input
                type="text"
                value={role.userCount}
                className="form-input"
                readOnly
                style={{ background: '#f9fafb' }}
              />
            </div>
            <div className="form-group">
              <label className="form-label">Default Role</label>
              <input
                type="text"
                value={role.isDefault ? 'Yes' : 'No'}
                className="form-input"
                readOnly
                style={{ background: '#f9fafb' }}
              />
            </div>
          </div>
          
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <div className="form-group">
              <label className="form-label">Created Date</label>
              <input
                type="text"
                value={role.createdDate}
                className="form-input"
                readOnly
                style={{ background: '#f9fafb' }}
              />
            </div>
            <div className="form-group">
              <label className="form-label">Created By</label>
              <input
                type="text"
                value={role.createdBy}
                className="form-input"
                readOnly
                style={{ background: '#f9fafb' }}
              />
            </div>
          </div>
          
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <div className="form-group">
              <label className="form-label">Last Modified</label>
              <input
                type="text"
                value={role.lastModified}
                className="form-input"
                readOnly
                style={{ background: '#f9fafb' }}
              />
            </div>
            <div className="form-group">
              <label className="form-label">Modified By</label>
              <input
                type="text"
                value={role.modifiedBy}
                className="form-input"
                readOnly
                style={{ background: '#f9fafb' }}
              />
            </div>
          </div>

          <div className="actions-bar" style={{ marginTop: '20px' }}>
            <div>
              <button type="button" className="btn-secondary" onClick={onEdit}>
                Edit Role
              </button>
            </div>
            <button type="button" className="btn-danger" onClick={onDelete}>
              Delete Role
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default RoleDetails;