import React from 'react';

const RolesList = ({ roles, selectedRole, onRoleSelect, onCreateRole }) => {
  return (
    <div className="roles-panel">
      <div className="panel-header">
        <h2 className="panel-title">Roles</h2>
        <button className="btn-primary" onClick={onCreateRole}>
          + New Role
        </button>
      </div>
      
      <ul className="roles-list">
        {roles.map((role) => (
          <li
            key={role.id}
            className={`role-item ${selectedRole?.id === role.id ? 'active' : ''}`}
            onClick={() => onRoleSelect(role)}
          >
            <div className="role-info">
              <h4>{role.name}</h4>
              <p>{role.description?.substring(0, 50)}...</p>
            </div>
            <span className="role-badge">{role.userCount} users</span>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default RolesList;