from functools import wraps
from typing import List, Union, Callable, Any
from fastapi import HTTPException, status, Depends
from ..models.auth import CurrentUser
from ..api.auth import get_current_user_dependency

def require_permissions(
    permissions: Union[str, List[str]], 
    require_all: bool = False,
    allow_self: bool = False,
    self_param: str = "user_id"
):
    """
    Decorator to require specific permissions for an endpoint.
    
    Args:
        permissions: Single permission string or list of permissions
        require_all: If True, user must have ALL permissions. If False, user needs ANY permission
        allow_self: If True, allows users to access their own resources even without permissions
        self_param: Parameter name that contains the user ID for self-access check
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Get current user from dependencies
            current_user = None
            for key, value in kwargs.items():
                if isinstance(value, CurrentUser):
                    current_user = value
                    break
            
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # Convert single permission to list
            perm_list = [permissions] if isinstance(permissions, str) else permissions
            
            # Check self-access if allowed
            if allow_self and self_param in kwargs:
                user_id = kwargs.get(self_param)
                if user_id and current_user.id == user_id:
                    return await func(*args, **kwargs)
            
            # Check permissions
            user_permissions = current_user.permissions
            
            # Check if user has all_permissions (super admin)
            if user_permissions.get('all_permissions', False):
                return await func(*args, **kwargs)
            
            if require_all:
                # User must have ALL permissions
                has_permissions = all(
                    user_permissions.get(perm, False) for perm in perm_list
                )
            else:
                # User must have ANY permission
                has_permissions = any(
                    user_permissions.get(perm, False) for perm in perm_list
                )
            
            if not has_permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient permissions. Required: {', '.join(perm_list)}"
                )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator

def require_role_management():
    """Decorator to require role_management permission"""
    return require_permissions("role_management")

def require_user_management():
    """Decorator to require user_management permission"""
    return require_permissions("user_management")

def require_role_assignment():
    """Decorator to require role_assignment or role_management permission"""
    return require_permissions(["role_assignment", "role_management"], require_all=False)

def require_admin():
    """Decorator to require admin-level permissions"""
    return require_permissions(["role_management", "user_management"], require_all=False)

def allow_self_or_admin(self_param: str = "user_id"):
    """Decorator to allow self-access or admin permissions"""
    return require_permissions("user_management", allow_self=True, self_param=self_param)

class PermissionChecker:
    """Class-based permission checker for more complex scenarios"""
    
    def __init__(self, current_user: CurrentUser):
        self.current_user = current_user
        self.permissions = current_user.permissions
    
    def has_permission(self, permission: str) -> bool:
        """Check if user has a specific permission"""
        return (
            self.permissions.get('all_permissions', False) or
            self.permissions.get(permission, False)
        )
    
    def has_any_permission(self, permissions: List[str]) -> bool:
        """Check if user has any of the specified permissions"""
        if self.permissions.get('all_permissions', False):
            return True
        return any(self.permissions.get(perm, False) for perm in permissions)
    
    def has_all_permissions(self, permissions: List[str]) -> bool:
        """Check if user has all of the specified permissions"""
        if self.permissions.get('all_permissions', False):
            return True
        return all(self.permissions.get(perm, False) for perm in permissions)
    
    def can_manage_roles(self) -> bool:
        """Check if user can manage roles"""
        return self.has_permission('role_management')
    
    def can_assign_roles(self) -> bool:
        """Check if user can assign roles"""
        return self.has_any_permission(['role_management', 'role_assignment'])
    
    def can_manage_users(self) -> bool:
        """Check if user can manage users"""
        return self.has_permission('user_management')
    
    def can_access_user_data(self, user_id: int) -> bool:
        """Check if user can access another user's data"""
        return (
            self.current_user.id == user_id or  # Self-access
            self.has_permission('user_management')  # Admin access
        )
    
    def can_view_audit_logs(self) -> bool:
        """Check if user can view audit logs"""
        return self.has_permission('audit_access')
    
    def can_modify_system_settings(self) -> bool:
        """Check if user can modify system settings"""
        return self.has_permission('system_settings')
    
    def require_permission(self, permission: str):
        """Raise exception if user doesn't have permission"""
        if not self.has_permission(permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required: {permission}"
            )
    
    def require_any_permission(self, permissions: List[str]):
        """Raise exception if user doesn't have any of the permissions"""
        if not self.has_any_permission(permissions):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required any of: {', '.join(permissions)}"
            )
    
    def require_all_permissions(self, permissions: List[str]):
        """Raise exception if user doesn't have all permissions"""
        if not self.has_all_permissions(permissions):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required all of: {', '.join(permissions)}"
            )

# Dependency functions for FastAPI
async def get_permission_checker(
    current_user: CurrentUser = Depends(get_current_user_dependency)
) -> PermissionChecker:
    """FastAPI dependency to get permission checker"""
    return PermissionChecker(current_user)

async def require_role_management_dependency(
    current_user: CurrentUser = Depends(get_current_user_dependency)
) -> CurrentUser:
    """FastAPI dependency to require role management permission"""
    checker = PermissionChecker(current_user)
    checker.require_permission('role_management')
    return current_user

async def require_user_management_dependency(
    current_user: CurrentUser = Depends(get_current_user_dependency)
) -> CurrentUser:
    """FastAPI dependency to require user management permission"""
    checker = PermissionChecker(current_user)
    checker.require_permission('user_management')
    return current_user

async def require_role_assignment_dependency(
    current_user: CurrentUser = Depends(get_current_user_dependency)
) -> CurrentUser:
    """FastAPI dependency to require role assignment permission"""
    checker = PermissionChecker(current_user)
    checker.require_any_permission(['role_management', 'role_assignment'])
    return current_user
