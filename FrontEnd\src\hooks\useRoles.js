import { useState, useEffect } from 'react';
import { DEFAULT_ROLES } from '../data/permissionsData';
import rbacService from '../services/rbacService';
import { toast } from 'react-toastify';

export const useRoles = () => {
  const [roles, setRoles] = useState([]);
  const [selectedRole, setSelectedRole] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchRoles();
  }, []);

  const fetchRoles = async () => {
    setLoading(true);
    try {
      const result = await rbacService.getAllRoles();

      if (result.success) {
        setRoles(result.roles);
        setError(null);

        // Set first role as selected if none selected
        if (!selectedRole && result.roles.length > 0) {
          setSelectedRole(result.roles[0]);
        }
      } else {
        setError(result.error);
        console.error('Failed to fetch roles:', result.error);

        // Fallback to mock data if API fails
        setRoles(DEFAULT_ROLES);
        if (!selectedRole && DEFAULT_ROLES.length > 0) {
          setSelectedRole(DEFAULT_ROLES[0]);
        }
      }
    } catch (err) {
      setError('Failed to fetch roles');
      console.error('Fetch roles error:', err);

      // Fallback to mock data
      setRoles(DEFAULT_ROLES);
      if (!selectedRole && DEFAULT_ROLES.length > 0) {
        setSelectedRole(DEFAULT_ROLES[0]);
      }
    } finally {
      setLoading(false);
    }
  };

  const createRole = async (roleData) => {
    try {
      const result = await rbacService.createRole(roleData);

      if (result.success) {
        // Add the new role to the list
        setRoles(prev => [...prev, result.role]);
        toast.success(result.message || 'Role created successfully');
        return result.role;
      } else {
        setError(result.error);
        toast.error(result.error || 'Failed to create role');
        throw new Error(result.error);
      }
    } catch (err) {
      setError('Failed to create role');
      console.error('Create role error:', err);
      throw err;
    }
  };

  const updateRole = async (roleId, roleData) => {
    try {
      const result = await rbacService.updateRole(roleId, roleData);

      if (result.success) {
        // Update the role in the list
        setRoles(prev => prev.map(role =>
          role.id === roleId ? result.role : role
        ));

        // Update selected role if it's the one being updated
        if (selectedRole && selectedRole.id === roleId) {
          setSelectedRole(result.role);
        }

        toast.success(result.message || 'Role updated successfully');
        return result.role;
      } else {
        setError(result.error);
        toast.error(result.error || 'Failed to update role');
        throw new Error(result.error);
      }
    } catch (err) {
      setError('Failed to update role');
      console.error('Update role error:', err);
      throw err;
    }
  };

  const deleteRole = async (roleId) => {
    try {
      const result = await rbacService.deleteRole(roleId);

      if (result.success) {
        // Remove the role from the list
        setRoles(prev => prev.filter(role => role.id !== roleId));

        // Clear selected role if it's the one being deleted
        if (selectedRole && selectedRole.id === roleId) {
          const remainingRoles = roles.filter(role => role.id !== roleId);
          setSelectedRole(remainingRoles.length > 0 ? remainingRoles[0] : null);
        }

        toast.success(result.message || 'Role deleted successfully');
        return true;
      } else {
        setError(result.error);
        toast.error(result.error || 'Failed to delete role');
        throw new Error(result.error);
      }
    } catch (err) {
      setError('Failed to delete role');
      console.error('Delete role error:', err);
      throw err;
    }
  };

  return {
    roles,
    selectedRole,
    setSelectedRole,
    loading,
    error,
    createRole,
    updateRole,
    deleteRole,
    fetchRoles
  };
};