export const PERMISSION_CATEGORIES = {
  USER_MANAGEMENT: {
    id: 'user_management',
    title: 'User Management',
    description: 'Control user accounts and profiles',
    permissions: [
      {
        id: 'create_users',
        name: 'Create Users',
        description: 'Add new users to the system'
      },
      {
        id: 'view_user_profiles',
        name: 'View User Profiles',
        description: 'Access user profiles and information'
      },
      {
        id: 'edit_user_information',
        name: 'Edit User Information',
        description: 'Modify user profiles and data'
      },
      {
        id: 'delete_deactivate_users',
        name: 'Delete/Deactivate Users',
        description: 'Remove or disable user accounts'
      },
      {
        id: 'assign_remove_roles',
        name: 'Assign/Remove Roles',
        description: 'Manage user role assignments'
      },
      {
        id: 'reset_user_passwords',
        name: 'Reset User Passwords',
        description: 'Reset passwords for user accounts'
      },
      {
        id: 'view_user_activity_logs',
        name: 'View User Activity Logs',
        description: 'Access user activity and audit trails'
      }
    ]
  },
  SYSTEM_ADMINISTRATION: {
    id: 'system_administration',
    title: 'System Administration',
    description: 'Configure system-wide settings and administration',
    permissions: [
      {
        id: 'access_admin_dashboard',
        name: 'Access Admin Dashboard',
        description: 'View administrative interface'
      },
      {
        id: 'modify_system_settings',
        name: 'Modify System Settings',
        description: 'Change system configuration'
      },
      {
        id: 'manage_integrations',
        name: 'Manage Integrations',
        description: 'Configure third-party integrations'
      },
      {
        id: 'view_system_logs',
        name: 'View System Logs',
        description: 'Access system activity logs'
      },
      {
        id: 'backup_restore_data',
        name: 'Backup/Restore Data',
        description: 'Manage system backups and restoration'
      },
      {
        id: 'manage_api_keys',
        name: 'Manage API Keys',
        description: 'Create and manage API access keys'
      }
    ]
  },
  DATA_REPORTS: {
    id: 'data_reports',
    title: 'Data & Reports',
    description: 'Access reports and data analytics',
    permissions: [
      {
        id: 'view_basic_reports',
        name: 'View Basic Reports',
        description: 'Access standard system reports'
      },
      {
        id: 'view_advanced_analytics',
        name: 'View Advanced Analytics',
        description: 'Access detailed analytics and insights'
      },
      {
        id: 'export_data',
        name: 'Export Data',
        description: 'Download data in various formats'
      },
      {
        id: 'create_custom_reports',
        name: 'Create Custom Reports',
        description: 'Build and save custom report templates'
      },
      {
        id: 'view_audit_logs',
        name: 'View Audit Logs',
        description: 'Access system audit and compliance logs'
      }
    ]
  },
  COMMUNICATION: {
    id: 'communication',
    title: 'Communication',
    description: 'Manage notifications and communication tools',
    permissions: [
      {
        id: 'send_notifications',
        name: 'Send Notifications',
        description: 'Send system notifications to users'
      },
      {
        id: 'broadcast_messages',
        name: 'Broadcast Messages',
        description: 'Send organization-wide messages'
      },
      {
        id: 'manage_email_templates',
        name: 'Manage Email Templates',
        description: 'Create and modify email templates'
      },
      {
        id: 'manage_announcements',
        name: 'Manage Announcements',
        description: 'Create and publish announcements'
      }
    ]
  }
};

export const DEFAULT_ROLES = [
  {
    id: 1,
    name: 'Administrator',
    type: 'System Role',
    description: 'Full system access with all permissions for managing users and system settings',
    status: 'Active',
    userCount: 12,
    isDefault: false,
    createdDate: '2024-01-15',
    createdBy: 'System Admin',
    lastModified: '2025-08-12',
    modifiedBy: 'John Doe',
    permissions: [
      'create_users', 'view_user_profiles', 'edit_user_information', 'delete_deactivate_users',
      'assign_remove_roles', 'reset_user_passwords', 'view_user_activity_logs',
      'access_admin_dashboard', 'modify_system_settings', 'manage_integrations',
      'view_system_logs', 'backup_restore_data', 'manage_api_keys',
      'view_basic_reports', 'view_advanced_analytics', 'export_data',
      'create_custom_reports', 'view_audit_logs',
      'send_notifications', 'broadcast_messages', 'manage_email_templates', 'manage_announcements'
    ]
  },
  {
    id: 2,
    name: 'Manager',
    type: 'Custom Role',
    description: 'Team management access with user and report permissions',
    status: 'Active',
    userCount: 8,
    isDefault: false,
    createdDate: '2024-02-10',
    createdBy: 'John Doe',
    lastModified: '2025-07-20',
    modifiedBy: 'Jane Smith',
    permissions: ['view_user_profiles', 'view_basic_reports', 'send_notifications']
  },
  {
    id: 3,
    name: 'Editor',
    type: 'Department Role',
    description: 'Content creation and editing capabilities',
    status: 'Active',
    userCount: 24,
    isDefault: false,
    createdDate: '2024-01-20',
    createdBy: 'John Doe',
    lastModified: '2025-06-15',
    modifiedBy: 'Bob Wilson',
    permissions: ['view_basic_reports', 'send_notifications']
  },
  {
    id: 4,
    name: 'Viewer',
    type: 'Custom Role',
    description: 'Read-only access to basic system features',
    status: 'Active',
    userCount: 45,
    isDefault: true,
    createdDate: '2024-01-15',
    createdBy: 'System Admin',
    lastModified: '2025-03-10',
    modifiedBy: 'Jane Smith',
    permissions: ['view_basic_reports']
  }
];