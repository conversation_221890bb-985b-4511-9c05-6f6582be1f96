from pydantic import BaseModel, EmailStr, Field
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum

# Token Models
class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int

class TokenData(BaseModel):
    user_id: Optional[int] = None
    username: Optional[str] = None
    email: Optional[str] = None
    roles: list = []
    permissions: Dict[str, Any] = {}

class RefreshTokenRequest(BaseModel):
    refresh_token: str

# Password Reset Models
class PasswordResetRequest(BaseModel):
    email: EmailStr

class PasswordResetConfirm(BaseModel):
    token: str
    new_password: str = Field(..., min_length=8, max_length=100)
    confirm_password: str
    
    def validate_passwords_match(self):
        if self.new_password != self.confirm_password:
            raise ValueError('Passwords do not match')
        return True

# Email Verification Models
class EmailVerificationRequest(BaseModel):
    email: EmailStr

class EmailVerificationConfirm(BaseModel):
    token: str

# Session Models
class SessionCreate(BaseModel):
    user_id: int
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None

class SessionResponse(BaseModel):
    id: int
    user_id: int
    session_token: str
    ip_address: Optional[str]
    user_agent: Optional[str]
    expires_at: datetime
    is_active: bool
    created_at: datetime
    
    class Config:
        from_attributes = True

# External Identity Models
class ExternalIdentityCreate(BaseModel):
    provider_name: str
    external_user_id: str
    external_username: Optional[str] = None
    external_email: Optional[str] = None
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None

class ExternalIdentityResponse(BaseModel):
    id: int
    provider_name: str
    external_user_id: str
    external_username: Optional[str]
    external_email: Optional[str]
    created_at: datetime
    
    class Config:
        from_attributes = True

# Activity Log Models
class ActivityLogCreate(BaseModel):
    user_id: Optional[int] = None
    activity_type: str
    activity_description: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class ActivityLogResponse(BaseModel):
    id: int
    user_id: Optional[int]
    activity_type: str
    activity_description: Optional[str]
    ip_address: Optional[str]
    user_agent: Optional[str]
    metadata: Optional[Dict[str, Any]]
    created_at: datetime
    
    class Config:
        from_attributes = True

# Authentication Response Models
class LoginResponse(BaseModel):
    user: Dict[str, Any]
    tokens: Token
    session: SessionResponse

class LogoutResponse(BaseModel):
    message: str
    logged_out_at: datetime

# Current User Model
class CurrentUser(BaseModel):
    id: int
    username: str
    email: str
    first_name: Optional[str]
    last_name: Optional[str]
    is_active: bool
    is_verified: bool
    roles: list = []
    permissions: Dict[str, Any] = {}
    last_login: Optional[datetime] = None
    
    class Config:
        from_attributes = True

# OAuth Models
class OAuthProvider(str, Enum):
    GOOGLE = "google"
    MICROSOFT = "microsoft"

class OAuthAuthorizationRequest(BaseModel):
    provider: OAuthProvider
    redirect_uri: Optional[str] = None

class OAuthAuthorizationResponse(BaseModel):
    authorization_url: str
    state: str

class OAuthCallbackRequest(BaseModel):
    code: str
    state: str
    provider: OAuthProvider

class OAuthUserInfo(BaseModel):
    provider: OAuthProvider
    provider_user_id: str
    email: EmailStr
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    avatar_url: Optional[str] = None
    verified_email: bool = False

class OAuthLoginResponse(BaseModel):
    user: "CurrentUser"
    tokens: Token
    is_new_user: bool = False
    linked_account: bool = False

class OAuthAccountLinkRequest(BaseModel):
    provider: OAuthProvider
    provider_user_id: str
    access_token: str
