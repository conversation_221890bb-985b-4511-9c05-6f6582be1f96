// Authentication Service
// Handles user authentication, registration, and session management

import apiService, { ApiError } from './api';

class AuthService {
  constructor() {
    this.currentUser = null;
    this.isAuthenticated = false;
  }

  // User Registration
  async register(userData) {
    try {
      // Transform frontend form data to backend format
      const registrationData = {
        email: userData.email,
        username: userData.username,
        password: userData.password,
        confirm_password: userData.confirmPassword,
        first_name: userData.firstName || userData.fullName?.split(' ')[0] || '',
        last_name: userData.lastName || userData.fullName?.split(' ').slice(1).join(' ') || '',
        phone: userData.phoneNumber || userData.phone || null,
        avatar_url: userData.avatarUrl || null
      };

      const response = await apiService.post('/api/auth/register', registrationData);
      
      return {
        success: true,
        user: response,
        message: 'User registered successfully'
      };
    } catch (error) {
      console.error('Registration error:', error);
      
      let errorMessage = 'Registration failed';
      if (error instanceof ApiError) {
        if (error.status === 400) {
          errorMessage = error.data?.detail || 'User with this email or username already exists';
        } else if (error.status === 422) {
          // Validation errors
          const errors = error.data?.errors || [];
          if (errors.length > 0) {
            errorMessage = errors.map(err => err.msg).join(', ');
          } else {
            errorMessage = error.data?.detail || 'Invalid input data';
          }
        } else {
          errorMessage = error.message;
        }
      }
      
      return {
        success: false,
        error: errorMessage,
        details: error.data
      };
    }
  }

  // User Login
  async login(credentials) {
    try {
      const loginData = {
        username: credentials.username,
        password: credentials.password
      };

      const response = await apiService.post('/api/auth/login', loginData);
      
      // Store tokens and user data
      if (response.tokens) {
        apiService.setToken(response.tokens.access_token);
        localStorage.setItem('refresh_token', response.tokens.refresh_token);
      }
      
      this.currentUser = response.user;
      this.isAuthenticated = true;
      
      return {
        success: true,
        user: response.user,
        tokens: response.tokens,
        permissions: response.permissions
      };
    } catch (error) {
      console.error('Login error:', error);
      
      let errorMessage = 'Login failed';
      if (error instanceof ApiError) {
        if (error.status === 401) {
          errorMessage = 'Invalid credentials';
        } else {
          errorMessage = error.message;
        }
      }
      
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  // Logout
  async logout() {
    try {
      // Clear tokens and user data
      apiService.setToken(null);
      localStorage.removeItem('refresh_token');
      this.currentUser = null;
      this.isAuthenticated = false;
      
      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      return { success: false, error: error.message };
    }
  }

  // Get current user
  async getCurrentUser() {
    try {
      const response = await apiService.get('/api/auth/me');
      this.currentUser = response;
      this.isAuthenticated = true;
      return response;
    } catch (error) {
      console.error('Get current user error:', error);
      this.currentUser = null;
      this.isAuthenticated = false;
      throw error;
    }
  }

  // Refresh token
  async refreshToken() {
    try {
      const refreshToken = localStorage.getItem('refresh_token');
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await apiService.post('/api/auth/refresh', {
        refresh_token: refreshToken
      });

      apiService.setToken(response.access_token);
      
      return response;
    } catch (error) {
      console.error('Token refresh error:', error);
      // If refresh fails, logout user
      await this.logout();
      throw error;
    }
  }

  // Check if user is authenticated
  isUserAuthenticated() {
    return this.isAuthenticated && this.currentUser !== null;
  }

  // Get stored token
  getStoredToken() {
    return localStorage.getItem('access_token');
  }

  // Initialize auth state from storage
  initializeAuth() {
    const token = this.getStoredToken();
    if (token) {
      apiService.setToken(token);
      // Optionally verify token by getting current user
      this.getCurrentUser().catch(() => {
        // Token is invalid, clear it
        this.logout();
      });
    }
  }
}

// Create singleton instance
const authService = new AuthService();

export default authService;
