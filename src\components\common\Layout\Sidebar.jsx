// src/components/common/Layout/Sidebar.jsx

import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

const Sidebar = () => {
  const location = useLocation();
  const [openMenus, setOpenMenus] = useState({});

  const toggleMenu = (menuKey) => {
    setOpenMenus(prev => ({
      ...prev,
      [menuKey]: !prev[menuKey]
    }));
  };

  // Updated menu items with User Roles & Permissions
  const menuItems = [
    {
      key: 'dashboard',
      title: 'Dashboard',
      path: '/dashboard',
      icon: '📊'
    },
    {
      key: 'accounts',
      title: 'Accounts',
      icon: '👥',
      submenu: [
        {
          key: 'account-management',
          title: 'Account Management',
          submenu: [
            {
              key: 'user-management',
              title: 'User Management',
              path: '/accounts/account-management/users'
            },
            {
              key: 'user-roles-permissions', // Add this new menu item
              title: 'User Roles & Permissions',
              path: '/accounts/account-management/user-roles-permissions'
            },
            {
              key: 'organization-settings',
              title: 'Organization Settings',
              path: '/accounts/account-management/settings'
            }
          ]
        },
        {
          key: 'create-user',
          title: 'Create New User',
          path: '/create-user'
        }
      ]
    },
    {
      key: 'reports',
      title: 'Reports',
      path: '/reports',
      icon: '📈'
    },
    {
      key: 'settings',
      title: 'Settings',
      path: '/settings',
      icon: '⚙️'
    }
  ];

  const renderMenuItem = (item, level = 0) => {
    const hasSubmenu = item.submenu && item.submenu.length > 0;
    const isOpen = openMenus[item.key];
    const isActive = location.pathname === item.path;

    return (
      <div key={item.key} className={`menu-item level-${level}`}>
        {hasSubmenu ? (
          <div
            className={`menu-header ${isOpen ? 'open' : ''}`}
            onClick={() => toggleMenu(item.key)}
          >
            <span className="menu-icon">{item.icon}</span>
            <span className="menu-title">{item.title}</span>
            <span className={`menu-arrow ${isOpen ? 'rotate' : ''}`}>▼</span>
          </div>
        ) : (
          <Link
            to={item.path}
            className={`menu-link ${isActive ? 'active' : ''}`}
          >
            {item.icon && <span className="menu-icon">{item.icon}</span>}
            <span className="menu-title">{item.title}</span>
          </Link>
        )}

        {hasSubmenu && isOpen && (
          <div className="submenu">
            {item.submenu.map(subItem => renderMenuItem(subItem, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <h2>NeuroIQ Admin</h2>
      </div>
      
      <nav className="sidebar-nav">
        {menuItems.map(item => renderMenuItem(item))}
      </nav>
    </div>
  );
};

export default Sidebar;

/* Add this CSS to your sidebar styles or create a separate CSS file */

/* Sidebar Styles */
.sidebar {
  width: 280px;
  height: 100vh;
  background: #1e293b;
  color: white;
  overflow-y: auto;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #334155;
}

.sidebar-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #f1f5f9;
}

.sidebar-nav {
  padding: 20px 0;
}

.menu-item {
  margin-bottom: 4px;
}

.menu-header {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.menu-header:hover {
  background: #334155;
}

.menu-header.open {
  background: #334155;
}

.menu-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #cbd5e1;
  text-decoration: none;
  transition: all 0.2s;
}

.menu-link:hover {
  background: #334155;
  color: #f1f5f9;
}

.menu-link.active {
  background: #3b82f6;
  color: white;
}

.menu-icon {
  margin-right: 12px;
  font-size: 16px;
}

.menu-title {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
}

.menu-arrow {
  font-size: 12px;
  transition: transform 0.2s;
}

.menu-arrow.rotate {
  transform: rotate(180deg);
}

.submenu {
  background: #0f172a;
}

.level-1 .menu-link {
  padding-left: 52px;
}

.level-2 .menu-link {
  padding-left: 72px;
  font-size: 13px;
}

.level-1 .menu-header {
  padding-left: 52px;
}

.level-2 .menu-header {
  padding-left: 72px;
  font-size: 13px;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: auto;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
  }
}