import React, { useState, useEffect } from 'react';

const UsersTable = ({ role }) => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);

  // Mock users data
  const mockUsers = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      department: 'Engineering',
      status: 'Active',
      lastActive: '2 hours ago',
      avatar: 'JD'
    },
    {
      id: 2,
      name: '<PERSON>',
      email: '<EMAIL>',
      department: 'Operations',
      status: 'Active',
      lastActive: '1 day ago',
      avatar: 'JS'
    },
    {
      id: 3,
      name: '<PERSON>',
      email: '<EMAIL>',
      department: 'Marketing',
      status: 'Inactive',
      lastActive: '1 week ago',
      avatar: 'BW'
    }
  ];

  useEffect(() => {
    fetchRoleUsers();
  }, [role]);

  const fetchRoleUsers = async () => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      const filteredUsers = mockUsers.slice(0, Math.min(role.userCount, 3));
      setUsers(filteredUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveUser = (userId) => {
    if (window.confirm('Are you sure you want to remove this user from the role?')) {
      setUsers(prev => prev.filter(user => user.id !== userId));
    }
  };

  const handleAssignUsers = () => {
    console.log('Open user assignment modal');
  };

  if (loading) {
    return (
      <div className="users-section">
        <div className="loading-spinner">Loading users...</div>
      </div>
    );
  }

  return (
    <div className="users-section">
      <div className="panel-header">
        <h2 className="panel-title">Users with {role.name} Role</h2>
        <button className="btn-primary" onClick={handleAssignUsers}>
          Assign Users
        </button>
      </div>
      
      {users.length === 0 ? (
        <div className="empty-state">
          <p>No users assigned to this role yet.</p>
          <button className="btn-primary" onClick={handleAssignUsers}>
            Assign Users
          </button>
        </div>
      ) : (
        <table className="users-table">
          <thead>
            <tr>
              <th>User</th>
              <th>Email</th>
              <th>Department</th>
              <th>Status</th>
              <th>Last Active</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {users.map((user) => (
              <tr key={user.id}>
                <td>
                  <div className="user-info">
                    <div className="user-avatar">{user.avatar}</div>
                    <div>
                      <div style={{ fontWeight: 500 }}>{user.name}</div>
                    </div>
                  </div>
                </td>
                <td>{user.email}</td>
                <td>{user.department}</td>
                <td>
                  <span className={`status-badge ${user.status.toLowerCase() === 'active' ? 'status-active' : 'status-inactive'}`}>
                    {user.status}
                  </span>
                </td>
                <td>{user.lastActive}</td>
                <td>
                  <button
                    className="btn-secondary"
                    style={{ padding: '4px 8px', fontSize: '12px' }}
                    onClick={() => handleRemoveUser(user.id)}
                  >
                    Remove
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
};

export default UsersTable;