import { useState, useEffect } from 'react';
import { PERMISSION_CATEGORIES } from '../data/permissionsData';
import rbacService from '../services/rbacService';
import { toast } from 'react-toastify';

export const usePermissions = (selectedRole) => {
  const [permissions, setPermissions] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (selectedRole) {
      initializePermissions();
    }
  }, [selectedRole]);

  const initializePermissions = () => {
    if (!selectedRole) {
      setPermissions({});
      return;
    }

    const rolePermissions = selectedRole.permissions || {};
    const permissionState = {};

    // Handle both array format (frontend) and object format (backend)
    if (Array.isArray(rolePermissions)) {
      // Frontend format - array of permission IDs
      Object.values(PERMISSION_CATEGORIES).forEach(category => {
        category.permissions.forEach(permission => {
          permissionState[permission.id] = rolePermissions.includes(permission.id);
        });
      });
    } else {
      // Backend format - object with permission keys
      Object.values(PERMISSION_CATEGORIES).forEach(category => {
        category.permissions.forEach(permission => {
          permissionState[permission.id] = rolePermissions[permission.id] === true;
        });
      });
    }

    setPermissions(permissionState);
  };

  const togglePermission = (permissionId) => {
    setPermissions(prev => ({
      ...prev,
      [permissionId]: !prev[permissionId]
    }));
  };

  const getActivePermissions = () => {
    return Object.entries(permissions)
      .filter(([_, isActive]) => isActive)
      .map(([permissionId]) => permissionId);
  };

  const savePermissions = async () => {
    if (!selectedRole) {
      toast.error('No role selected');
      return;
    }

    setLoading(true);
    try {
      const activePermissions = getActivePermissions();

      // Convert permissions array to object format for backend
      const permissionsObject = {};
      activePermissions.forEach(permissionId => {
        permissionsObject[permissionId] = true;
      });

      // Update role with new permissions
      const roleData = {
        role_name: selectedRole.role_name,
        role_description: selectedRole.role_description,
        permissions: permissionsObject
      };

      const result = await rbacService.updateRole(selectedRole.id, roleData);

      if (result.success) {
        toast.success('Permissions updated successfully');
        return activePermissions;
      } else {
        toast.error(result.error || 'Failed to update permissions');
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Save permissions error:', error);
      toast.error('Failed to save permissions');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    permissions,
    togglePermission,
    savePermissions,
    getActivePermissions,
    loading
  };
};