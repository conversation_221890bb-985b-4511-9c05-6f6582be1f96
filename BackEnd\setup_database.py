#!/usr/bin/env python3
"""
Database Setup Script for NeuroIQ User Management System
"""

import mysql.connector
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def setup_database():
    """Set up the database schema and initial data"""
    
    # Database configuration
    config = {
        'host': os.getenv('DB_HOST', 'localhost'),
        'port': int(os.getenv('DB_PORT', 3306)),
        'user': os.getenv('DB_USER', 'root'),
        'password': os.getenv('DB_PASSWORD', 'admin'),
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    try:
        print("Connecting to MySQL server...")
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        # Create database if it doesn't exist
        db_name = os.getenv('DB_NAME', 'NeuroIQ')
        print(f"Creating database '{db_name}' if it doesn't exist...")
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {db_name}")
        
        # Use the database
        cursor.execute(f"USE {db_name}")
        
        # Read and execute schema file
        print("Creating database schema...")
        with open('database/schema.sql', 'r', encoding='utf-8') as file:
            schema_sql = file.read()
            
        # Split by semicolon and execute each statement
        statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
        for statement in statements:
            if statement:
                cursor.execute(statement)
        
        # Read and execute initial data file
        print("Inserting initial data...")
        with open('database/init_data.sql', 'r', encoding='utf-8') as file:
            init_sql = file.read()
            
        # Split by semicolon and execute each statement
        statements = [stmt.strip() for stmt in init_sql.split(';') if stmt.strip()]
        for statement in statements:
            if statement:
                cursor.execute(statement)
        
        # Commit changes
        connection.commit()
        
        print("✅ Database setup completed successfully!")
        print(f"Database: {db_name}")
        print("Default admin user created:")
        print("  Email: <EMAIL>")
        print("  Username: admin")
        print("  Password: admin123")
        print("⚠️  Please change the default admin password!")
        
    except mysql.connector.Error as err:
        print(f"❌ Database error: {err}")
        return False
    except FileNotFoundError as err:
        print(f"❌ File not found: {err}")
        print("Make sure you're running this script from the backend directory")
        return False
    except Exception as err:
        print(f"❌ Unexpected error: {err}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals() and connection.is_connected():
            connection.close()
            print("MySQL connection closed.")
    
    return True

def test_connection():
    """Test database connection"""
    config = {
        'host': os.getenv('DB_HOST', 'localhost'),
        'port': int(os.getenv('DB_PORT', 3306)),
        'user': os.getenv('DB_USER', 'root'),
        'password': os.getenv('DB_PASSWORD', 'admin'),
        'database': os.getenv('DB_NAME', 'NeuroIQ'),
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    try:
        print("Testing database connection...")
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        # Test query
        cursor.execute("SELECT COUNT(*) FROM neuroiq_users")
        result = cursor.fetchone()
        
        print(f"✅ Connection successful! Found {result[0]} users in database.")
        
        cursor.close()
        connection.close()
        return True
        
    except mysql.connector.Error as err:
        print(f"❌ Connection failed: {err}")
        return False

if __name__ == "__main__":
    print("NeuroIQ Database Setup")
    print("=" * 50)
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("Please create a .env file with database configuration.")
        exit(1)
    
    # Setup database
    if setup_database():
        print("\n" + "=" * 50)
        test_connection()
    else:
        print("❌ Database setup failed!")
        exit(1)
