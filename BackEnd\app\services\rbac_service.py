from typing import Optional, List, Dict, Any
from datetime import datetime
import logging
from fastapi import HTTPException, status

from ..config.database import db_manager
from ..models.user import (
    RoleCreate, RoleUpdate, RoleResponse, 
    UserRoleAssignment, UserRoleAssignmentResponse
)

logger = logging.getLogger(__name__)

class RBACService:
    def __init__(self):
        self.db = db_manager
    
    # Role Management Methods
    async def create_role(self, role_data: RoleCreate) -> RoleResponse:
        """Create a new role"""
        try:
            # Check if role already exists
            existing_role = await self.get_role_by_name(role_data.role_name)
            if existing_role:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Role with this name already exists"
                )
            
            # Insert role
            query = """
                INSERT INTO user_roles (role_name, role_description, permissions)
                VALUES (%s, %s, %s)
            """
            
            import json
            permissions_json = json.dumps(role_data.permissions) if role_data.permissions else "{}"
            
            role_id = await self.db.execute_insert(
                query, 
                (role_data.role_name, role_data.role_description, permissions_json)
            )
            
            # Get the created role
            created_role = await self.get_role_by_id(role_id)
            if not created_role:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create role"
                )
            
            return created_role
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating role: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create role"
            )
    
    async def get_role_by_id(self, role_id: int) -> Optional[RoleResponse]:
        """Get role by ID"""
        try:
            query = "SELECT * FROM user_roles WHERE id = %s AND is_active = TRUE"
            result = await self.db.execute_query(query, (role_id,))
            
            if not result:
                return None
            
            role_data = result[0]
            
            # Parse permissions JSON
            if role_data['permissions']:
                import json
                if isinstance(role_data['permissions'], str):
                    role_data['permissions'] = json.loads(role_data['permissions'])
            else:
                role_data['permissions'] = {}
            
            return RoleResponse(**role_data)
            
        except Exception as e:
            logger.error(f"Error getting role by ID: {e}")
            return None
    
    async def get_role_by_name(self, role_name: str) -> Optional[RoleResponse]:
        """Get role by name"""
        try:
            query = "SELECT * FROM user_roles WHERE role_name = %s AND is_active = TRUE"
            result = await self.db.execute_query(query, (role_name,))
            
            if not result:
                return None
            
            role_data = result[0]
            
            # Parse permissions JSON
            if role_data['permissions']:
                import json
                if isinstance(role_data['permissions'], str):
                    role_data['permissions'] = json.loads(role_data['permissions'])
            else:
                role_data['permissions'] = {}
            
            return RoleResponse(**role_data)
            
        except Exception as e:
            logger.error(f"Error getting role by name: {e}")
            return None
    
    async def get_all_roles(self, include_inactive: bool = False) -> List[RoleResponse]:
        """Get all roles"""
        try:
            if include_inactive:
                query = "SELECT * FROM user_roles ORDER BY role_name"
            else:
                query = "SELECT * FROM user_roles WHERE is_active = TRUE ORDER BY role_name"
            
            result = await self.db.execute_query(query)
            
            roles = []
            for role_data in result:
                # Parse permissions JSON
                if role_data['permissions']:
                    import json
                    if isinstance(role_data['permissions'], str):
                        role_data['permissions'] = json.loads(role_data['permissions'])
                else:
                    role_data['permissions'] = {}
                
                roles.append(RoleResponse(**role_data))
            
            return roles
            
        except Exception as e:
            logger.error(f"Error getting all roles: {e}")
            return []
    
    async def update_role(self, role_id: int, role_data: RoleUpdate) -> Optional[RoleResponse]:
        """Update role"""
        try:
            # Check if role exists
            existing_role = await self.get_role_by_id(role_id)
            if not existing_role:
                return None
            
            # Build update query dynamically
            update_fields = []
            params = []
            
            if role_data.role_description is not None:
                update_fields.append("role_description = %s")
                params.append(role_data.role_description)
            
            if role_data.permissions is not None:
                update_fields.append("permissions = %s")
                import json
                params.append(json.dumps(role_data.permissions))
            
            if role_data.is_active is not None:
                update_fields.append("is_active = %s")
                params.append(role_data.is_active)
            
            if not update_fields:
                return existing_role  # No changes
            
            # Add updated_at
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            params.append(role_id)
            
            query = f"UPDATE user_roles SET {', '.join(update_fields)} WHERE id = %s"
            
            await self.db.execute_query(query, params)
            
            # Return updated role
            return await self.get_role_by_id(role_id)
            
        except Exception as e:
            logger.error(f"Error updating role: {e}")
            return None
    
    async def delete_role(self, role_id: int) -> bool:
        """Soft delete role (deactivate)"""
        try:
            # Check if role exists
            existing_role = await self.get_role_by_id(role_id)
            if not existing_role:
                return False
            
            # Check if role is assigned to any users
            assignment_query = """
                SELECT COUNT(*) as count 
                FROM user_role_assignments 
                WHERE role_id = %s AND is_active = TRUE
            """
            result = await self.db.execute_query(assignment_query, (role_id,))
            
            if result[0]['count'] > 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Cannot delete role that is assigned to users"
                )
            
            # Soft delete (deactivate)
            query = "UPDATE user_roles SET is_active = FALSE WHERE id = %s"
            await self.db.execute_query(query, (role_id,))
            
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting role: {e}")
            return False

    # Role Assignment Methods
    async def assign_role_to_user(self, user_id: int, role_id: int, assigned_by: int, expires_at: Optional[datetime] = None) -> Optional[UserRoleAssignmentResponse]:
        """Assign role to user"""
        try:
            # Check if user exists
            user_query = "SELECT id FROM neuroiq_users WHERE id = %s AND is_active = TRUE"
            user_result = await self.db.execute_query(user_query, (user_id,))
            if not user_result:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            # Check if role exists
            role = await self.get_role_by_id(role_id)
            if not role:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Role not found"
                )

            # Check if assignment already exists
            existing_query = """
                SELECT id FROM user_role_assignments
                WHERE user_id = %s AND role_id = %s AND is_active = TRUE
            """
            existing_result = await self.db.execute_query(existing_query, (user_id, role_id))
            if existing_result:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="User already has this role assigned"
                )

            # Create assignment
            assignment_query = """
                INSERT INTO user_role_assignments (user_id, role_id, assigned_by, expires_at)
                VALUES (%s, %s, %s, %s)
            """

            assignment_id = await self.db.execute_insert(
                assignment_query,
                (user_id, role_id, assigned_by, expires_at)
            )

            # Get the created assignment
            return await self.get_role_assignment_by_id(assignment_id)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error assigning role to user: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to assign role"
            )

    async def remove_role_from_user(self, user_id: int, role_id: int) -> bool:
        """Remove role from user"""
        try:
            # Check if assignment exists
            query = """
                SELECT id FROM user_role_assignments
                WHERE user_id = %s AND role_id = %s AND is_active = TRUE
            """
            result = await self.db.execute_query(query, (user_id, role_id))

            if not result:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Role assignment not found"
                )

            # Deactivate assignment
            update_query = """
                UPDATE user_role_assignments
                SET is_active = FALSE
                WHERE user_id = %s AND role_id = %s AND is_active = TRUE
            """

            await self.db.execute_query(update_query, (user_id, role_id))

            return True

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error removing role from user: {e}")
            return False

    async def get_user_roles(self, user_id: int) -> List[UserRoleAssignmentResponse]:
        """Get all roles assigned to a user"""
        try:
            query = """
                SELECT ura.*, r.role_name
                FROM user_role_assignments ura
                JOIN user_roles r ON ura.role_id = r.id
                WHERE ura.user_id = %s AND ura.is_active = TRUE AND r.is_active = TRUE
                ORDER BY ura.assigned_at DESC
            """

            result = await self.db.execute_query(query, (user_id,))

            assignments = []
            for assignment_data in result:
                assignments.append(UserRoleAssignmentResponse(**assignment_data))

            return assignments

        except Exception as e:
            logger.error(f"Error getting user roles: {e}")
            return []

    async def get_role_users(self, role_id: int) -> List[UserRoleAssignmentResponse]:
        """Get all users assigned to a role"""
        try:
            query = """
                SELECT ura.*, r.role_name
                FROM user_role_assignments ura
                JOIN user_roles r ON ura.role_id = r.id
                WHERE ura.role_id = %s AND ura.is_active = TRUE AND r.is_active = TRUE
                ORDER BY ura.assigned_at DESC
            """

            result = await self.db.execute_query(query, (role_id,))

            assignments = []
            for assignment_data in result:
                assignments.append(UserRoleAssignmentResponse(**assignment_data))

            return assignments

        except Exception as e:
            logger.error(f"Error getting role users: {e}")
            return []

    async def get_role_assignment_by_id(self, assignment_id: int) -> Optional[UserRoleAssignmentResponse]:
        """Get role assignment by ID"""
        try:
            query = """
                SELECT ura.*, r.role_name
                FROM user_role_assignments ura
                JOIN user_roles r ON ura.role_id = r.id
                WHERE ura.id = %s
            """

            result = await self.db.execute_query(query, (assignment_id,))

            if not result:
                return None

            return UserRoleAssignmentResponse(**result[0])

        except Exception as e:
            logger.error(f"Error getting role assignment by ID: {e}")
            return None

    # Permission Methods
    async def check_user_permission(self, user_id: int, permission: str) -> bool:
        """Check if user has a specific permission"""
        try:
            query = """
                SELECT r.permissions
                FROM user_role_assignments ura
                JOIN user_roles r ON ura.role_id = r.id
                WHERE ura.user_id = %s AND ura.is_active = TRUE AND r.is_active = TRUE
            """

            result = await self.db.execute_query(query, (user_id,))

            # Check permissions from all roles
            for row in result:
                if row['permissions']:
                    import json
                    permissions = row['permissions']
                    if isinstance(permissions, str):
                        permissions = json.loads(permissions)

                    # Check if user has the specific permission
                    if permissions.get(permission, False):
                        return True

                    # Check if user has all_permissions
                    if permissions.get('all_permissions', False):
                        return True

            return False

        except Exception as e:
            logger.error(f"Error checking user permission: {e}")
            return False

    async def get_user_permissions(self, user_id: int) -> Dict[str, Any]:
        """Get all permissions for a user (merged from all roles)"""
        try:
            query = """
                SELECT r.permissions
                FROM user_role_assignments ura
                JOIN user_roles r ON ura.role_id = r.id
                WHERE ura.user_id = %s AND ura.is_active = TRUE AND r.is_active = TRUE
            """

            result = await self.db.execute_query(query, (user_id,))

            # Merge permissions from all roles
            merged_permissions = {}
            for row in result:
                if row['permissions']:
                    import json
                    permissions = row['permissions']
                    if isinstance(permissions, str):
                        permissions = json.loads(permissions)
                    merged_permissions.update(permissions)

            return merged_permissions

        except Exception as e:
            logger.error(f"Error getting user permissions: {e}")
            return {}
