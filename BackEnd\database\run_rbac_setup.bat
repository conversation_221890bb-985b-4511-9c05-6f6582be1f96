@echo off
setlocal enabledelayedexpansion

REM NeuroIQ RBAC Database Setup Script for Windows
REM This script sets up the complete RBAC system by running all SQL scripts in the correct order

REM Configuration
set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=NeuroIQ
set DB_USER=root
set DB_PASSWORD=

echo ==============================================
echo NeuroIQ RBAC Database Setup
echo ==============================================
echo.

REM Get database password if not provided
if "%DB_PASSWORD%"=="" (
    set /p DB_PASSWORD="Enter MySQL password for user %DB_USER%: "
)

echo [INFO] Checking MySQL connection...

REM Check MySQL connection
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -e "SELECT 1;" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Cannot connect to MySQL. Please check your connection parameters.
    pause
    exit /b 1
)
echo [SUCCESS] MySQL connection successful

echo [INFO] Checking if database %DB_NAME% exists...

REM Check if database exists
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -e "USE %DB_NAME%;" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Database %DB_NAME% does not exist. Creating it...
    mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -e "CREATE DATABASE IF NOT EXISTS %DB_NAME%;"
    if errorlevel 1 (
        echo [ERROR] Failed to create database %DB_NAME%
        pause
        exit /b 1
    )
    echo [SUCCESS] Database %DB_NAME% created successfully
) else (
    echo [SUCCESS] Database %DB_NAME% exists
)

echo.
echo [INFO] Starting RBAC setup process...
echo.

REM Step 1: Run main schema if it doesn't exist
if exist "schema.sql" (
    echo [INFO] Checking if main schema needs to be applied...
    mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% %DB_NAME% -e "DESCRIBE neuroiq_users;" >nul 2>&1
    if errorlevel 1 (
        echo [INFO] Executing Main database schema...
        mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% %DB_NAME% < "schema.sql"
        if errorlevel 1 (
            echo [ERROR] Main schema setup failed. Aborting.
            pause
            exit /b 1
        )
        echo [SUCCESS] Main database schema completed successfully
    ) else (
        echo [SUCCESS] Main schema already exists
    )
) else (
    echo [WARNING] Main schema.sql not found. Assuming it's already applied.
)

REM Step 2: Apply RBAC enhancements
echo [INFO] Executing RBAC database enhancements...
if not exist "setup_rbac_database.sql" (
    echo [ERROR] File setup_rbac_database.sql not found!
    pause
    exit /b 1
)

mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% %DB_NAME% < "setup_rbac_database.sql"
if errorlevel 1 (
    echo [ERROR] RBAC setup failed. Aborting.
    pause
    exit /b 1
)
echo [SUCCESS] RBAC database enhancements completed successfully

REM Step 3: Apply additional RBAC schema
echo [INFO] Executing Additional RBAC tables and views...
if exist "rbac_schema.sql" (
    mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% %DB_NAME% < "rbac_schema.sql"
    if errorlevel 1 (
        echo [WARNING] Additional RBAC schema failed, but continuing...
    ) else (
        echo [SUCCESS] Additional RBAC tables and views completed successfully
    )
) else (
    echo [WARNING] rbac_schema.sql not found. Skipping additional RBAC schema.
)

REM Step 4: Insert initial RBAC data
echo [INFO] Executing RBAC initial data and default roles...
if exist "rbac_init_data.sql" (
    mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% %DB_NAME% < "rbac_init_data.sql"
    if errorlevel 1 (
        echo [WARNING] RBAC initial data failed, but continuing...
    ) else (
        echo [SUCCESS] RBAC initial data and default roles completed successfully
    )
) else (
    echo [WARNING] rbac_init_data.sql not found. Skipping initial RBAC data.
)

REM Step 5: Apply existing init_data.sql if it exists
if exist "init_data.sql" (
    echo [INFO] Executing Additional initial data...
    mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% %DB_NAME% < "init_data.sql"
    if errorlevel 1 (
        echo [WARNING] Additional initial data failed, but continuing...
    ) else (
        echo [SUCCESS] Additional initial data completed successfully
    )
)

echo.
echo ==============================================
echo [SUCCESS] RBAC Setup Complete!
echo ==============================================
echo.
echo [INFO] Summary of what was set up:
echo   ✓ Enhanced user_roles table with hierarchy and system role support
echo   ✓ Enhanced user_role_assignments table with audit trail
echo   ✓ Permission categories and granular permissions system
echo   ✓ Role-permission mapping tables
echo   ✓ RBAC audit logging system
echo   ✓ Useful views for querying role assignments
echo   ✓ Default roles with comprehensive permissions
echo   ✓ Role hierarchy for permission inheritance
echo.
echo [INFO] Next steps:
echo   1. Test the RBAC API endpoints
echo   2. Create admin users and assign appropriate roles
echo   3. Customize roles and permissions for your specific needs
echo.

pause
