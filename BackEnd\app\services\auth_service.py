from typing import Op<PERSON>, Dict, Any
from datetime import datetime, timedelta
import logging
from fastapi import HTTPException, status, Request

from ..config.database import db_manager
from ..models.auth import (
    Token, TokenData, LoginResponse, SessionCreate, SessionResponse,
    ActivityLogCreate, CurrentUser, PasswordResetRequest, PasswordResetConfirm,
    EmailVerificationRequest, EmailVerificationConfirm
)
from ..models.user import UserLogin, UserResponse
from ..utils.security import security
from .user_service import UserService

logger = logging.getLogger(__name__)

class AuthService:
    def __init__(self):
        self.db = db_manager
        self.user_service = UserService()
    
    async def authenticate_user(self, login_data: UserLogin) -> Optional[UserResponse]:
        """Authenticate user with username/email and password"""
        try:
            # Get user by username or email
            user = None
            if "@" in login_data.username:
                user = await self.user_service.get_user_by_email(login_data.username)
            else:
                user = await self.user_service.get_user_by_username(login_data.username)
            
            if not user:
                return None
            
            # Get password hash
            query = "SELECT password_hash FROM neuroiq_users WHERE id = %s"
            result = await self.db.execute_query(query, (user.id,))
            
            if not result:
                return None
            
            password_hash = result[0]['password_hash']
            
            # Verify password
            if not security.verify_password(login_data.password, password_hash):
                return None
            
            return user
            
        except Exception as e:
            logger.error(f"Error authenticating user: {e}")
            return None
    
    async def login(self, login_data: UserLogin, request: Request) -> LoginResponse:
        """Login user and create session"""
        try:
            # Authenticate user
            user = await self.authenticate_user(login_data)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid credentials"
                )
            
            # Check if user is active
            if not user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Account is deactivated"
                )
            
            # Get user permissions
            permissions = await self.get_user_permissions(user.id)
            
            # Create tokens
            token_data = {
                "user_id": user.id,
                "username": user.username,
                "email": user.email,
                "roles": user.roles
            }
            
            access_token = security.create_access_token(token_data)
            refresh_token = security.create_refresh_token({"user_id": user.id})
            
            # Create session
            session_data = SessionCreate(
                user_id=user.id,
                ip_address=request.client.host if request.client else None,
                user_agent=request.headers.get("user-agent")
            )
            
            session = await self.create_session(session_data, access_token, refresh_token)
            
            # Update last login
            await self.update_last_login(user.id)
            
            # Log activity
            await self.log_activity(
                user.id,
                "login",
                "User logged in successfully",
                request.client.host if request.client else None,
                request.headers.get("user-agent")
            )
            
            # Create tokens response
            tokens = Token(
                access_token=access_token,
                refresh_token=refresh_token,
                token_type="bearer",
                expires_in=30 * 60  # 30 minutes
            )
            
            # Create user response with permissions
            user_dict = user.dict()
            user_dict["permissions"] = permissions
            
            return LoginResponse(
                user=user_dict,
                tokens=tokens,
                session=session
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error during login: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Login failed"
            )
    
    async def refresh_token(self, refresh_token: str) -> Token:
        """Refresh access token using refresh token"""
        try:
            # Verify refresh token
            payload = security.verify_token(refresh_token, "refresh")
            user_id = payload.get("user_id")
            
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid refresh token"
                )
            
            # Check if session exists and is active
            session_query = """
                SELECT * FROM user_sessions 
                WHERE refresh_token = %s AND user_id = %s AND is_active = TRUE
                AND expires_at > NOW()
            """
            session_result = await self.db.execute_query(session_query, (refresh_token, user_id))
            
            if not session_result:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid or expired refresh token"
                )
            
            # Get user
            user = await self.user_service.get_user_by_id(user_id)
            if not user or not user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User not found or inactive"
                )
            
            # Create new tokens
            token_data = {
                "user_id": user.id,
                "username": user.username,
                "email": user.email,
                "roles": user.roles
            }
            
            new_access_token = security.create_access_token(token_data)
            new_refresh_token = security.create_refresh_token({"user_id": user.id})
            
            # Update session with new tokens
            update_session_query = """
                UPDATE user_sessions 
                SET session_token = %s, refresh_token = %s, updated_at = CURRENT_TIMESTAMP
                WHERE refresh_token = %s AND user_id = %s
            """
            
            await self.db.execute_update(
                update_session_query, 
                (new_access_token, new_refresh_token, refresh_token, user_id)
            )
            
            return Token(
                access_token=new_access_token,
                refresh_token=new_refresh_token,
                token_type="bearer",
                expires_in=30 * 60
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error refreshing token: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Token refresh failed"
            )

    async def logout(self, access_token: str, user_id: int) -> bool:
        """Logout user and invalidate session"""
        try:
            # Deactivate session
            query = """
                UPDATE user_sessions
                SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
                WHERE session_token = %s AND user_id = %s
            """

            await self.db.execute_update(query, (access_token, user_id))

            # Log activity
            await self.log_activity(user_id, "logout", "User logged out")

            return True

        except Exception as e:
            logger.error(f"Error during logout: {e}")
            return False

    async def create_session(self, session_data: SessionCreate, access_token: str, refresh_token: str) -> SessionResponse:
        """Create a new user session"""
        try:
            expires_at = datetime.utcnow() + timedelta(days=7)  # 7 days for refresh token

            query = """
                INSERT INTO user_sessions (
                    user_id, session_token, refresh_token, ip_address, user_agent, expires_at
                ) VALUES (%s, %s, %s, %s, %s, %s)
            """

            session_id = await self.db.execute_insert(
                query,
                (
                    session_data.user_id,
                    access_token,
                    refresh_token,
                    session_data.ip_address,
                    session_data.user_agent,
                    expires_at
                )
            )

            # Get created session
            session_query = "SELECT * FROM user_sessions WHERE id = %s"
            result = await self.db.execute_query(session_query, (session_id,))

            return SessionResponse(**result[0])

        except Exception as e:
            logger.error(f"Error creating session: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create session"
            )

    async def get_current_user(self, token: str) -> CurrentUser:
        """Get current user from token"""
        try:
            # Verify token
            payload = security.verify_token(token, "access")
            user_id = payload.get("user_id")

            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token"
                )

            # Check if session is active
            session_query = """
                SELECT * FROM user_sessions
                WHERE session_token = %s AND user_id = %s AND is_active = TRUE
            """
            session_result = await self.db.execute_query(session_query, (token, user_id))

            if not session_result:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Session not found or expired"
                )

            # Get user
            user = await self.user_service.get_user_by_id(user_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User not found"
                )

            # Get permissions
            permissions = await self.get_user_permissions(user_id)

            return CurrentUser(
                id=user.id,
                username=user.username,
                email=user.email,
                first_name=user.first_name,
                last_name=user.last_name,
                is_active=user.is_active,
                is_verified=user.is_verified,
                roles=user.roles,
                permissions=permissions,
                last_login=user.last_login
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting current user: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials"
            )

    async def get_user_permissions(self, user_id: int) -> Dict[str, Any]:
        """Get user permissions based on roles"""
        try:
            query = """
                SELECT r.permissions
                FROM user_role_assignments ura
                JOIN user_roles r ON ura.role_id = r.id
                WHERE ura.user_id = %s AND ura.is_active = TRUE AND r.is_active = TRUE
            """

            result = await self.db.execute_query(query, (user_id,))

            # Merge permissions from all roles
            merged_permissions = {}
            for row in result:
                if row['permissions']:
                    permissions = row['permissions']
                    if isinstance(permissions, str):
                        import json
                        permissions = json.loads(permissions)
                    merged_permissions.update(permissions)

            return merged_permissions

        except Exception as e:
            logger.error(f"Error getting user permissions: {e}")
            return {}

    async def update_last_login(self, user_id: int) -> bool:
        """Update user's last login timestamp"""
        try:
            query = """
                UPDATE neuroiq_users
                SET last_login = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
                WHERE id = %s
            """

            await self.db.execute_update(query, (user_id,))
            return True

        except Exception as e:
            logger.error(f"Error updating last login: {e}")
            return False

    async def log_activity(self, user_id: Optional[int], activity_type: str,
                          description: Optional[str] = None, ip_address: Optional[str] = None,
                          user_agent: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Log user activity"""
        try:
            query = """
                INSERT INTO user_activity_log (
                    user_id, activity_type, activity_description, ip_address, user_agent, metadata
                ) VALUES (%s, %s, %s, %s, %s, %s)
            """

            metadata_json = None
            if metadata:
                import json
                metadata_json = json.dumps(metadata)

            await self.db.execute_insert(
                query,
                (user_id, activity_type, description, ip_address, user_agent, metadata_json)
            )

            return True

        except Exception as e:
            logger.error(f"Error logging activity: {e}")
            return False
