#!/usr/bin/env python3
"""
Update admin user password in database
"""

import mysql.connector
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

def update_admin_password():
    """Update admin user password"""
    
    # Database configuration
    config = {
        'host': os.getenv('DB_HOST', 'localhost'),
        'port': int(os.getenv('DB_PORT', 3306)),
        'user': os.getenv('DB_USER', 'root'),
        'password': os.getenv('DB_PASSWORD', 'admin'),
        'database': os.getenv('DB_NAME', 'NeuroIQ'),
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    try:
        print("Connecting to MySQL database...")
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        # Update admin password
        new_hash = '$2b$12$BcCKSQgmdIQX3nOTbbRPUetg0OlYRoagP987XAQneGL.MkLAWEpT6'
        
        query = """
            UPDATE neuroiq_users 
            SET password_hash = %s, updated_at = CURRENT_TIMESTAMP
            WHERE username = 'admin'
        """
        
        cursor.execute(query, (new_hash,))
        connection.commit()
        
        print("✅ Admin password updated successfully!")
        
        cursor.close()
        connection.close()
        
    except mysql.connector.Error as err:
        print(f"❌ Database error: {err}")
        return False
    except Exception as err:
        print(f"❌ Unexpected error: {err}")
        return False
    
    return True

if __name__ == "__main__":
    update_admin_password()
