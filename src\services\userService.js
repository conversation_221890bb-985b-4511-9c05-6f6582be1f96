// User Service
// Handles user management operations

import apiService, { ApiError } from './api';

class UserService {
  // Get current user profile
  async getCurrentUserProfile() {
    try {
      const response = await apiService.get('/api/users/me');
      return {
        success: true,
        user: response
      };
    } catch (error) {
      console.error('Get current user profile error:', error);
      return {
        success: false,
        error: error.message || 'Failed to fetch user profile'
      };
    }
  }

  // Update current user profile
  async updateCurrentUserProfile(userData) {
    try {
      const updateData = {
        first_name: userData.firstName || userData.first_name,
        last_name: userData.lastName || userData.last_name,
        phone: userData.phone || userData.phoneNumber,
        avatar_url: userData.avatarUrl || userData.avatar_url
      };

      const response = await apiService.put('/api/users/me', updateData);
      return {
        success: true,
        user: response,
        message: 'Profile updated successfully'
      };
    } catch (error) {
      console.error('Update profile error:', error);
      return {
        success: false,
        error: error.message || 'Failed to update profile'
      };
    }
  }

  // Update user password
  async updatePassword(passwordData) {
    try {
      const updateData = {
        current_password: passwordData.currentPassword,
        new_password: passwordData.newPassword,
        confirm_password: passwordData.confirmPassword
      };

      await apiService.put('/api/users/me/password', updateData);
      return {
        success: true,
        message: 'Password updated successfully'
      };
    } catch (error) {
      console.error('Update password error:', error);
      
      let errorMessage = 'Failed to update password';
      if (error instanceof ApiError) {
        if (error.status === 400) {
          errorMessage = error.data?.detail || 'Current password is incorrect';
        } else if (error.status === 422) {
          errorMessage = 'Password validation failed';
        }
      }
      
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  // Get all users (admin only)
  async getAllUsers(params = {}) {
    try {
      const queryParams = {
        page: params.page || 1,
        size: params.size || 10,
        search: params.search || '',
        role: params.role || '',
        status: params.status || ''
      };

      const response = await apiService.get('/api/users', queryParams);
      return {
        success: true,
        users: response.users,
        pagination: {
          total: response.total,
          page: response.page,
          size: response.size,
          pages: response.pages
        }
      };
    } catch (error) {
      console.error('Get users error:', error);
      return {
        success: false,
        error: error.message || 'Failed to fetch users'
      };
    }
  }

  // Get user by ID (admin only)
  async getUserById(userId) {
    try {
      const response = await apiService.get(`/api/users/${userId}`);
      return {
        success: true,
        user: response
      };
    } catch (error) {
      console.error('Get user error:', error);
      return {
        success: false,
        error: error.message || 'Failed to fetch user'
      };
    }
  }

  // Update user by ID (admin only)
  async updateUserById(userId, userData) {
    try {
      const updateData = {
        first_name: userData.firstName || userData.first_name,
        last_name: userData.lastName || userData.last_name,
        phone: userData.phone || userData.phoneNumber,
        avatar_url: userData.avatarUrl || userData.avatar_url,
        is_active: userData.isActive !== undefined ? userData.isActive : userData.is_active
      };

      const response = await apiService.put(`/api/users/${userId}`, updateData);
      return {
        success: true,
        user: response,
        message: 'User updated successfully'
      };
    } catch (error) {
      console.error('Update user error:', error);
      return {
        success: false,
        error: error.message || 'Failed to update user'
      };
    }
  }

  // Delete user by ID (admin only)
  async deleteUserById(userId) {
    try {
      await apiService.delete(`/api/users/${userId}`);
      return {
        success: true,
        message: 'User deleted successfully'
      };
    } catch (error) {
      console.error('Delete user error:', error);
      return {
        success: false,
        error: error.message || 'Failed to delete user'
      };
    }
  }

  // Search users
  async searchUsers(searchTerm, filters = {}) {
    try {
      const params = {
        search: searchTerm,
        ...filters
      };

      const response = await apiService.get('/api/users/search', params);
      return {
        success: true,
        users: response.users,
        total: response.total
      };
    } catch (error) {
      console.error('Search users error:', error);
      return {
        success: false,
        error: error.message || 'Failed to search users'
      };
    }
  }
}

// Create singleton instance
const userService = new UserService();

export default userService;
