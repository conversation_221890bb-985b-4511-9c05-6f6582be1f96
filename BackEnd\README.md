# NeuroIQ User Management API

A comprehensive FastAPI-based user management system for the NeuroIQ Enterprise AI Platform.

## Features

- **User Registration & Authentication**: JWT-based authentication with secure password hashing
- **User Profile Management**: Complete user profile CRUD operations
- **Role-Based Access Control**: Granular permissions system
- **Session Management**: Secure session handling with refresh tokens
- **Activity Logging**: Comprehensive audit trail
- **Password Reset**: Secure password reset functionality
- **Email Verification**: Email verification system
- **MySQL Database**: Robust database schema with connection pooling

## Technology Stack

- **FastAPI**: Modern, fast web framework for building APIs
- **MySQL**: Reliable relational database
- **JWT**: JSON Web Tokens for authentication
- **Pydantic**: Data validation using Python type annotations
- **Bcrypt**: Secure password hashing
- **Uvicorn**: ASGI server for running the application

## Prerequisites

- Python 3.8+
- MySQL 8.0+
- pip (Python package manager)

## Installation

1. **Clone the repository** (if applicable):
   ```bash
   cd backend
   ```

2. **Create a virtual environment**:
   ```bash
   python -m venv venv
   
   # On Windows
   venv\Scripts\activate
   
   # On macOS/Linux
   source venv/bin/activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up MySQL database**:
   ```bash
   # Connect to MySQL as root
   mysql -u root -p
   
   # Create database and user
   CREATE DATABASE NeuroIQ;
   
   # Run the schema script
   mysql -u root -p NeuroIQ < database/schema.sql
   
   # Run the initial data script
   mysql -u root -p NeuroIQ < database/init_data.sql
   ```

5. **Configure environment variables**:
   - Copy `.env` file and update the values:
   ```bash
   # Update database credentials in .env file
   DB_HOST=localhost
   DB_USER=root
   DB_PASSWORD=admin
   DB_NAME=NeuroIQ
   DB_PORT=3306
   
   # Update JWT secret (use a strong secret in production)
   JWT_SECRET_KEY=your_super_secret_jwt_key_change_this_in_production
   ```

## Running the Application

1. **Start the development server**:
   ```bash
   python run.py
   ```
   
   Or using uvicorn directly:
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **Access the API**:
   - API Documentation: http://localhost:8000/docs
   - Alternative Documentation: http://localhost:8000/redoc
   - Health Check: http://localhost:8000/health

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `POST /api/auth/refresh` - Refresh access token
- `POST /api/auth/logout` - Logout user
- `GET /api/auth/me` - Get current user info
- `POST /api/auth/password-reset/request` - Request password reset
- `POST /api/auth/password-reset/confirm` - Confirm password reset
- `POST /api/auth/email-verification/request` - Request email verification
- `POST /api/auth/email-verification/confirm` - Confirm email verification

### Users
- `GET /api/users/me` - Get current user profile
- `PUT /api/users/me` - Update current user profile
- `PUT /api/users/me/password` - Update current user password
- `GET /api/users/{user_id}` - Get user by ID (admin only)
- `PUT /api/users/{user_id}` - Update user by ID (admin only)
- `GET /api/users/` - Get list of users (admin only)
- `DELETE /api/users/{user_id}` - Deactivate user (admin only)

## Default Admin User

The system comes with a default admin user:
- **Email**: <EMAIL>
- **Username**: admin
- **Password**: admin123

**⚠️ Important**: Change the default admin password immediately in production!

## Database Schema

The system uses the following main tables:
- `neuroiq_users` - User accounts
- `user_roles` - Role definitions
- `user_role_assignments` - User-role relationships
- `user_sessions` - Active user sessions
- `user_activity_log` - Activity audit trail
- `password_reset_tokens` - Password reset tokens
- `email_verification_tokens` - Email verification tokens

## Security Features

- **Password Hashing**: Bcrypt with configurable rounds
- **JWT Tokens**: Secure access and refresh tokens
- **Session Management**: Database-backed session tracking
- **Activity Logging**: Comprehensive audit trail
- **CORS Protection**: Configurable CORS settings
- **Input Validation**: Pydantic-based request validation

## Development

### Project Structure
```
backend/
├── app/
│   ├── api/           # API route handlers
│   ├── config/        # Configuration files
│   ├── models/        # Pydantic models
│   ├── services/      # Business logic
│   ├── utils/         # Utility functions
│   └── main.py        # FastAPI application
├── database/          # Database scripts
├── requirements.txt   # Python dependencies
├── .env              # Environment variables
└── run.py            # Startup script
```

### Adding New Features

1. Create models in `app/models/`
2. Implement business logic in `app/services/`
3. Create API endpoints in `app/api/`
4. Update database schema if needed
5. Add tests

## Testing

The API can be tested using:
- **Swagger UI**: http://localhost:8000/docs
- **Postman**: Import the OpenAPI spec from http://localhost:8000/openapi.json
- **curl**: Command line testing

### Example API Calls

1. **Register a new user**:
   ```bash
   curl -X POST "http://localhost:8000/api/auth/register" \
        -H "Content-Type: application/json" \
        -d '{
          "email": "<EMAIL>",
          "username": "testuser",
          "password": "password123",
          "confirm_password": "password123",
          "first_name": "Test",
          "last_name": "User"
        }'
   ```

2. **Login**:
   ```bash
   curl -X POST "http://localhost:8000/api/auth/login" \
        -H "Content-Type: application/json" \
        -d '{
          "username": "testuser",
          "password": "password123"
        }'
   ```

## Production Deployment

For production deployment:

1. **Update environment variables**:
   - Set `DEBUG=False`
   - Use a strong `JWT_SECRET_KEY`
   - Configure proper database credentials
   - Set appropriate `CORS_ORIGINS`

2. **Use a production ASGI server**:
   ```bash
   pip install gunicorn
   gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker
   ```

3. **Set up reverse proxy** (nginx, Apache, etc.)

4. **Configure SSL/TLS** for HTTPS

5. **Set up monitoring and logging**

## Troubleshooting

### Common Issues

1. **Database Connection Error**:
   - Check MySQL is running
   - Verify database credentials in `.env`
   - Ensure database `NeuroIQ` exists

2. **Import Errors**:
   - Ensure virtual environment is activated
   - Install all requirements: `pip install -r requirements.txt`

3. **Permission Errors**:
   - Check user roles and permissions
   - Verify JWT token is valid

### Logs

Check application logs for detailed error information. Logs include:
- Database connection status
- Authentication attempts
- API request/response details
- Error stack traces

## Support

For issues and questions:
1. Check the API documentation at `/docs`
2. Review the logs for error details
3. Verify database schema and data
4. Check environment configuration
